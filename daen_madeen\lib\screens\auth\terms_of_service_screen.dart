import 'package:flutter/material.dart';

/// Terms of Service Screen for دائن مدين (Creditor-Debtor) system
class TermsOfServiceScreen extends StatefulWidget {
  const TermsOfServiceScreen({super.key});

  @override
  State<TermsOfServiceScreen> createState() => _TermsOfServiceScreenState();
}

class _TermsOfServiceScreenState extends State<TermsOfServiceScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late ScrollController _scrollController;
  bool _hasScrolledToBottom = false;
  bool _hasAgreed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));
    
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 50) {
      if (!_hasScrolledToBottom) {
        setState(() {
          _hasScrolledToBottom = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              FadeTransition(
                opacity: _fadeAnimation,
                child: Container(
                  padding: const EdgeInsets.all(24),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const Expanded(
                        child: Text(
                          'شروط الخدمة',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 48), // Balance the back button
                    ],
                  ),
                ),
              ),

              // Content
              Expanded(
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 24),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Terms Content
                        Expanded(
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // App Icon and Title
                                Center(
                                  child: Column(
                                    children: [
                                      Container(
                                        width: 80,
                                        height: 80,
                                        decoration: BoxDecoration(
                                          color: const Color(0xFF10B981).withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(20),
                                        ),
                                        child: const Icon(
                                          Icons.gavel,
                                          size: 40,
                                          color: Color(0xFF10B981),
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      const Text(
                                        'سياسة الخصوصية لتطبيق دائن مدين',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF1F2937),
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'آخر تحديث: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFF6B7280),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                
                                const SizedBox(height: 30),
                                
                                _buildSection(
                                  '1. قبول الشروط',
                                  'باستخدام تطبيق "دائن مدين"، فإنك توافق على الالتزام بهذه الشروط والأحكام. إذا كنت لا توافق على أي من هذه الشروط، يرجى عدم استخدام التطبيق.',
                                ),
                                
                                _buildSection(
                                  '2. وصف الخدمة',
                                  'تطبيق "دائن مدين" هو نظام لإدارة الديون والمدفوعات يساعد أصحاب الأعمال على تتبع معاملاتهم المالية مع العملاء بطريقة منظمة وآمنة.',
                                ),
                                
                                _buildSection(
                                  '3. التسجيل والحساب',
                                  '• يجب تقديم معلومات صحيحة ودقيقة عند التسجيل\n• أنت مسؤول عن الحفاظ على سرية كلمة المرور\n• يجب إخطارنا فوراً بأي استخدام غير مصرح به لحسابك\n• لا يُسمح بإنشاء حسابات متعددة لنفس المستخدم',
                                ),
                                
                                _buildSection(
                                  '4. الاستخدام المقبول',
                                  '• استخدام التطبيق للأغراض القانونية فقط\n• عدم محاولة اختراق أو إلحاق الضرر بالنظام\n• عدم مشاركة معلومات حساسة مع أطراف غير مخولة\n• احترام حقوق المستخدمين الآخرين',
                                ),
                                
                                _buildSection(
                                  '5. المسؤوليات والضمانات',
                                  'التطبيق يُقدم "كما هو" دون ضمانات صريحة أو ضمنية. نحن غير مسؤولين عن أي أضرار مباشرة أو غير مباشرة قد تنتج عن استخدام التطبيق.',
                                ),
                                
                                _buildSection(
                                  '6. الملكية الفكرية',
                                  'جميع حقوق الملكية الفكرية للتطبيق محفوظة. لا يُسمح بنسخ أو توزيع أو تعديل أي جزء من التطبيق دون إذن مكتوب.',
                                ),
                                
                                _buildSection(
                                  '7. إنهاء الخدمة',
                                  'نحتفظ بالحق في إنهاء أو تعليق حسابك في أي وقت دون إشعار مسبق في حالة انتهاك هذه الشروط.',
                                ),
                                
                                _buildSection(
                                  '8. تعديل الشروط',
                                  'نحتفظ بالحق في تعديل هذه الشروط في أي وقت. سيتم إشعار المستخدمين بأي تغييرات جوهرية.',
                                ),
                                
                                _buildSection(
                                  '9. القانون المطبق',
                                  'تخضع هذه الشروط لقوانين الدولة الذي تقيم فيها، وأي نزاعات ستُحل وفقاً للقوانين المحلية.',
                                ),
                                
                                _buildSection(
                                  '10. الاتصال',
                                  'للاستفسارات حول شروط الخدمة:\nالبريد الإلكتروني: <EMAIL> \nالهاتف: +967 777747150',
                                ),
                                
                                const SizedBox(height: 30),
                              ],
                            ),
                          ),
                        ),
                        
                        // Agreement Section
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF9FAFB),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: const Color(0xFFE5E7EB),
                            ),
                          ),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Checkbox(
                                    value: _hasAgreed,
                                    onChanged: _hasScrolledToBottom ? (value) {
                                      setState(() {
                                        _hasAgreed = value ?? false;
                                      });
                                    } : null,
                                    activeColor: const Color(0xFF10B981),
                                  ),
                                  const Expanded(
                                    child: Text(
                                      'لقد قرأت وفهمت وأوافق على شروط الخدمة',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF374151),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              
                              if (!_hasScrolledToBottom)
                                const Padding(
                                  padding: EdgeInsets.only(top: 8),
                                  child: Text(
                                    'يرجى قراءة شروط الخدمة كاملة للمتابعة',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Color(0xFFEF4444),
                                    ),
                                  ),
                                ),
                              
                              const SizedBox(height: 16),
                              
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: _hasAgreed ? () {
                                        Navigator.pop(context, true);
                                      } : null,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: const Color(0xFF10B981),
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(vertical: 16),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        elevation: 2,
                                      ),
                                      child: const Text(
                                        'موافق ومتابعة',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                  
                                  const SizedBox(width: 12),
                                  
                                  Expanded(
                                    child: OutlinedButton(
                                      onPressed: () {
                                        Navigator.pop(context, false);
                                      },
                                      style: OutlinedButton.styleFrom(
                                        foregroundColor: const Color(0xFF6B7280),
                                        padding: const EdgeInsets.symmetric(vertical: 16),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        side: const BorderSide(
                                          color: Color(0xFFD1D5DB),
                                        ),
                                      ),
                                      child: const Text(
                                        'رفض',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF4B5563),
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
