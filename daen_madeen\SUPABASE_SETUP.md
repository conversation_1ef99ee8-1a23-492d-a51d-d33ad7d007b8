# Supabase Setup Guide for دائن مدين (Creditor-Debtor) System

## Prerequisites
- Supabase account (sign up at https://supabase.com)
- Flutter development environment set up

## Step 1: Create Supabase Project

1. Go to https://supabase.com and sign in
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `daen-madeen` or `creditor-debtor`
   - **Database Password**: Choose a strong password (save it securely)
   - **Region**: Choose the closest region to your users
5. Click "Create new project"
6. Wait for the project to be set up (usually takes 2-3 minutes)

## Step 2: Configure Database Schema

1. In your Supabase dashboard, go to the **SQL Editor**
2. Copy and paste the contents of `supabase_schema.sql` into the SQL editor
3. Click "Run" to execute the schema creation
4. Copy and paste the contents of `supabase_rls_policies.sql` into the SQL editor
5. Click "Run" to execute the RLS policies

## Step 3: Configure Authentication

1. Go to **Authentication** > **Settings** in your Supabase dashboard
2. Configure the following settings:

### General Settings
- **Site URL**: `http://localhost:3000` (for development)
- **Redirect URLs**: Add your app's redirect URLs when ready for production

### Email Settings
- Configure your email provider (recommended: use Supabase's built-in email for development)
- For production, set up a custom SMTP provider

### Auth Providers
- **Email**: Enable email authentication
- **Phone**: Enable if you want phone number authentication
- Disable social providers unless specifically needed

## Step 4: Get Project Credentials

1. Go to **Settings** > **API** in your Supabase dashboard
2. Copy the following values (you'll need them in your Flutter app):
   - **Project URL**: `https://your-project-id.supabase.co`
   - **Anon Key**: `eyJ...` (public key for client-side use)
   - **Service Role Key**: `eyJ...` (secret key - keep secure, server-side only)

## Step 5: Configure Flutter App

Create a file `lib/utils/supabase_config.dart` with your credentials:

```dart
class SupabaseConfig {
  static const String supabaseUrl = 'https://your-project-id.supabase.co';
  static const String supabaseAnonKey = 'your-anon-key-here';
}
```

**Important**: Never commit the service role key to version control!

## Step 6: Test Database Connection

1. In the SQL Editor, run this test query:
```sql
SELECT * FROM business_owners LIMIT 1;
```
2. You should see an empty result (no errors)

## Step 7: Set Up Real-time Subscriptions

1. Go to **Database** > **Replication** in your Supabase dashboard
2. Enable real-time for the following tables:
   - `debts`
   - `payments`
   - `notifications`
   - `customers`

## Step 8: Configure Storage (Optional)

If you plan to add file uploads (receipts, documents):
1. Go to **Storage** in your Supabase dashboard
2. Create a new bucket called `documents`
3. Set appropriate policies for file access

## Environment Variables

For production, set up environment variables:
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY` (server-side only)

## Security Checklist

- ✅ RLS policies are enabled on all tables
- ✅ Service role key is kept secure
- ✅ Authentication is properly configured
- ✅ Real-time subscriptions are limited to necessary tables
- ✅ Database backups are enabled (automatic in Supabase)

## Troubleshooting

### Common Issues:

1. **Connection Error**: Check if your project URL and anon key are correct
2. **RLS Policy Error**: Ensure all policies are properly created
3. **Authentication Error**: Verify auth settings and redirect URLs
4. **Real-time Not Working**: Check if replication is enabled for the table

### Useful SQL Queries for Testing:

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public';

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies WHERE schemaname = 'public';

-- Test user creation
INSERT INTO auth.users (email, encrypted_password) 
VALUES ('<EMAIL>', crypt('password123', gen_salt('bf')));
```

## Next Steps

After completing the Supabase setup:
1. Test the connection in your Flutter app
2. Implement authentication flow
3. Test CRUD operations
4. Set up real-time listeners
5. Deploy to production with proper environment variables

## Support

- Supabase Documentation: https://supabase.com/docs
- Supabase Discord: https://discord.supabase.com
- Flutter Supabase Package: https://pub.dev/packages/supabase_flutter
