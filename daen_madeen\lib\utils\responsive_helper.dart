import 'package:flutter/material.dart';

/// مساعد التصميم المتجاوب للتطبيق
class ResponsiveHelper {
  
  /// الحصول على عرض الشاشة
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// الحصول على ارتفاع الشاشة
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// تحديد نوع الجهاز حسب العرض
  static bool isSmallScreen(BuildContext context) => getScreenWidth(context) < 360;
  static bool isMediumScreen(BuildContext context) => getScreenWidth(context) >= 360 && getScreenWidth(context) < 600;
  static bool isLargeScreen(BuildContext context) => getScreenWidth(context) >= 600;
  static bool isTablet(BuildContext context) => getScreenWidth(context) >= 768;

  /// الحصول على حجم نص متجاوب
  static double getResponsiveFontSize(BuildContext context, double baseSize) {
    double screenWidth = getScreenWidth(context);
    
    if (isSmallScreen(context)) {
      return baseSize * 0.85; // تصغير النص للشاشات الصغيرة
    } else if (isLargeScreen(context)) {
      return baseSize * 1.15; // تكبير النص للشاشات الكبيرة
    }
    return baseSize; // الحجم الأساسي للشاشات المتوسطة
  }

  /// الحصول على مسافة متجاوبة
  static double getResponsivePadding(BuildContext context, double basePadding) {
    if (isSmallScreen(context)) {
      return basePadding * 0.75; // تقليل المسافة للشاشات الصغيرة
    } else if (isLargeScreen(context)) {
      return basePadding * 1.25; // زيادة المسافة للشاشات الكبيرة
    }
    return basePadding; // المسافة الأساسية للشاشات المتوسطة
  }

  /// الحصول على مسافة أفقية متجاوبة
  static double getResponsiveHorizontalPadding(BuildContext context) {
    if (isSmallScreen(context)) {
      return 16.0; // مسافة أقل للشاشات الصغيرة
    } else if (isLargeScreen(context)) {
      return 32.0; // مسافة أكبر للشاشات الكبيرة
    }
    return 20.0; // المسافة الافتراضية
  }

  /// الحصول على عدد الأعمدة المناسب للشبكة
  static int getGridCrossAxisCount(BuildContext context) {
    if (isSmallScreen(context)) {
      return 2; // عمودين للشاشات الصغيرة
    } else if (isTablet(context)) {
      return 3; // ثلاثة أعمدة للتابلت
    }
    return 2; // عمودين للشاشات المتوسطة
  }

  /// الحصول على نسبة العرض إلى الارتفاع للمربعات
  static double getGridChildAspectRatio(BuildContext context) {
    if (isSmallScreen(context)) {
      return 1.3; // نسبة أقل للشاشات الصغيرة (مربعات أطول)
    } else if (isLargeScreen(context)) {
      return 1.7; // نسبة أكبر للشاشات الكبيرة (مربعات أعرض)
    }
    return 1.5; // النسبة الافتراضية
  }

  /// الحصول على مسافة بين عناصر الشبكة
  static double getGridSpacing(BuildContext context) {
    if (isSmallScreen(context)) {
      return 8.0; // مسافة أقل للشاشات الصغيرة
    } else if (isLargeScreen(context)) {
      return 16.0; // مسافة أكبر للشاشات الكبيرة
    }
    return 12.0; // المسافة الافتراضية
  }

  /// الحصول على ارتفاع شريط التمرير للصور
  static double getSliderHeight(BuildContext context) {
    double screenHeight = getScreenHeight(context);
    
    if (isSmallScreen(context)) {
      return screenHeight * 0.18; // 18% من ارتفاع الشاشة للشاشات الصغيرة
    } else if (isLargeScreen(context)) {
      return screenHeight * 0.25; // 25% من ارتفاع الشاشة للشاشات الكبيرة
    }
    return 160.0; // الارتفاع الافتراضي
  }

  /// الحصول على حجم الأيقونة المناسب
  static double getResponsiveIconSize(BuildContext context, double baseSize) {
    if (isSmallScreen(context)) {
      return baseSize * 0.9;
    } else if (isLargeScreen(context)) {
      return baseSize * 1.2;
    }
    return baseSize;
  }

  /// الحصول على نصف قطر الحواف المناسب
  static double getResponsiveBorderRadius(BuildContext context, double baseRadius) {
    if (isSmallScreen(context)) {
      return baseRadius * 0.8;
    } else if (isLargeScreen(context)) {
      return baseRadius * 1.2;
    }
    return baseRadius;
  }

  /// الحصول على ارتفاع الأزرار المناسب
  static double getButtonHeight(BuildContext context) {
    if (isSmallScreen(context)) {
      return 45.0;
    } else if (isLargeScreen(context)) {
      return 55.0;
    }
    return 50.0;
  }

  /// الحصول على حجم الصورة الرمزية
  static double getAvatarSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 35.0;
    } else if (isLargeScreen(context)) {
      return 45.0;
    }
    return 40.0;
  }

  /// طباعة معلومات الشاشة للتطوير
  static void debugScreenInfo(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;
    
    print('📱 معلومات الشاشة:');
    print('   العرض: ${size.width.toStringAsFixed(1)}px');
    print('   الارتفاع: ${size.height.toStringAsFixed(1)}px');
    print('   نوع الشاشة: ${_getScreenType(context)}');
    print('   Safe Area - أعلى: ${padding.top}, أسفل: ${padding.bottom}');
  }

  static String _getScreenType(BuildContext context) {
    if (isSmallScreen(context)) return 'صغيرة';
    if (isLargeScreen(context)) return 'كبيرة';
    if (isTablet(context)) return 'تابلت';
    return 'متوسطة';
  }
}

/// إضافة extension للسهولة
extension ResponsiveContext on BuildContext {
  bool get isSmallScreen => ResponsiveHelper.isSmallScreen(this);
  bool get isMediumScreen => ResponsiveHelper.isMediumScreen(this);
  bool get isLargeScreen => ResponsiveHelper.isLargeScreen(this);
  bool get isTablet => ResponsiveHelper.isTablet(this);
  
  double responsiveFontSize(double baseSize) => ResponsiveHelper.getResponsiveFontSize(this, baseSize);
  double responsivePadding(double basePadding) => ResponsiveHelper.getResponsivePadding(this, basePadding);
  double get responsiveHorizontalPadding => ResponsiveHelper.getResponsiveHorizontalPadding(this);
  
  int get gridCrossAxisCount => ResponsiveHelper.getGridCrossAxisCount(this);
  double get gridChildAspectRatio => ResponsiveHelper.getGridChildAspectRatio(this);
  double get gridSpacing => ResponsiveHelper.getGridSpacing(this);
  
  double get sliderHeight => ResponsiveHelper.getSliderHeight(this);
  double responsiveIconSize(double baseSize) => ResponsiveHelper.getResponsiveIconSize(this, baseSize);
  double responsiveBorderRadius(double baseRadius) => ResponsiveHelper.getResponsiveBorderRadius(this, baseRadius);
  
  double get buttonHeight => ResponsiveHelper.getButtonHeight(this);
  double get avatarSize => ResponsiveHelper.getAvatarSize(this);
}
