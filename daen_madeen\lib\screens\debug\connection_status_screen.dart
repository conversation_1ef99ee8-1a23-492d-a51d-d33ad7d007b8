import 'package:flutter/material.dart';
import '../../utils/connection_checker.dart';

/// شاشة فحص حالة الاتصال بـ Supabase
class ConnectionStatusScreen extends StatefulWidget {
  const ConnectionStatusScreen({super.key});

  @override
  State<ConnectionStatusScreen> createState() => _ConnectionStatusScreenState();
}

class _ConnectionStatusScreenState extends State<ConnectionStatusScreen> {
  Map<String, dynamic>? _report;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkConnection();
  }

  Future<void> _checkConnection() async {
    setState(() => _isLoading = true);
    
    final report = await ConnectionChecker.getFullReport();
    
    setState(() {
      _report = report;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حالة الاتصال بـ Supabase'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkConnection,
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade50,
              Colors.white,
            ],
          ),
        ),
        child: _isLoading ? _buildLoading() : _buildReport(),
      ),
    );
  }

  Widget _buildLoading() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'جاري فحص الاتصال...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildReport() {
    if (_report == null) {
      return const Center(
        child: Text('خطأ في تحميل التقرير'),
      );
    }

    final configStatus = _report!['configStatus'] as ConnectionStatus;
    final connectionStatus = _report!['connectionStatus'] as ConnectionStatus?;
    final isReady = _report!['isReady'] as bool;
    final recommendations = _report!['recommendations'] as List<String>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // حالة عامة
          _buildStatusCard(
            title: 'الحالة العامة',
            status: isReady ? 'جاهز للاستخدام' : 'يتطلب إعداد',
            icon: isReady ? Icons.check_circle : Icons.warning,
            color: isReady ? Colors.green : Colors.orange,
            isMain: true,
          ),
          
          const SizedBox(height: 16),
          
          // حالة الإعدادات
          _buildStatusCard(
            title: 'إعدادات Supabase',
            status: ConnectionChecker.getStatusMessage(configStatus),
            icon: _getIconData(configStatus),
            color: _getStatusColor(configStatus),
          ),
          
          const SizedBox(height: 16),
          
          // حالة الاتصال
          if (connectionStatus != null)
            _buildStatusCard(
              title: 'اختبار الاتصال',
              status: ConnectionChecker.getStatusMessage(connectionStatus),
              icon: _getIconData(connectionStatus),
              color: _getStatusColor(connectionStatus),
            ),
          
          const SizedBox(height: 24),
          
          // التوصيات
          if (recommendations.isNotEmpty) ...[
            const Text(
              'التوصيات:',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...recommendations.map((rec) => _buildRecommendationCard(rec)),
          ],
          
          const SizedBox(height: 24),
          
          // أزرار الإجراءات
          _buildActionButtons(isReady),
          
          const SizedBox(height: 24),
          
          // معلومات إضافية
          _buildInfoSection(),
        ],
      ),
    );
  }

  Widget _buildStatusCard({
    required String title,
    required String status,
    required IconData icon,
    required Color color,
    bool isMain = false,
  }) {
    return Card(
      elevation: isMain ? 8 : 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: isMain ? 32 : 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: isMain ? 18 : 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    status,
                    style: TextStyle(
                      fontSize: isMain ? 16 : 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationCard(String recommendation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Icon(
              Icons.lightbulb_outline,
              color: Colors.amber.shade700,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                recommendation,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(bool isReady) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _checkConnection,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة فحص الاتصال'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade800,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(height: 12),
        if (!isReady)
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                // فتح دليل الإعداد
                showDialog(
                  context: context,
                  builder: (context) => _buildSetupDialog(),
                );
              },
              icon: const Icon(Icons.settings),
              label: const Text('دليل الإعداد السريع'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.blue.shade800,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات إضافية:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('وضع التطبيق', 'تجريبي (Demo Mode)'),
            _buildInfoRow('قاعدة البيانات المحلية', 'Hive - تعمل'),
            _buildInfoRow('الواجهات', 'جميع الشاشات تعمل'),
            _buildInfoRow('المصادقة', 'تجريبية (بدون Supabase)'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildSetupDialog() {
    return AlertDialog(
      title: const Text('دليل الإعداد السريع'),
      content: const SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('لإعداد Supabase:'),
            SizedBox(height: 8),
            Text('1. اذهب إلى https://supabase.com'),
            Text('2. أنشئ مشروع جديد'),
            Text('3. احصل على Project URL و Anon Key'),
            Text('4. حدّث ملف supabase_config.dart'),
            Text('5. شغّل ملفات SQL'),
            SizedBox(height: 12),
            Text(
              'للتفاصيل الكاملة، راجع ملف SUPABASE_QUICK_SETUP.md',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('حسناً'),
        ),
      ],
    );
  }

  IconData _getIconData(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return Icons.check_circle;
      case ConnectionStatus.configured:
        return Icons.settings;
      case ConnectionStatus.notConfigured:
        return Icons.settings_outlined;
      case ConnectionStatus.invalidUrl:
      case ConnectionStatus.invalidKey:
        return Icons.warning;
      case ConnectionStatus.connectionFailed:
      case ConnectionStatus.error:
        return Icons.error;
    }
  }

  Color _getStatusColor(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return Colors.green;
      case ConnectionStatus.configured:
        return Colors.blue;
      case ConnectionStatus.notConfigured:
        return Colors.orange;
      case ConnectionStatus.invalidUrl:
      case ConnectionStatus.invalidKey:
        return Colors.amber;
      case ConnectionStatus.connectionFailed:
      case ConnectionStatus.error:
        return Colors.red;
    }
  }
}
