import 'package:uuid/uuid.dart';

/// Payment status enum
enum PaymentStatus {
  pending,
  completed,
  cancelled;

  /// Convert from string (from database)
  static PaymentStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return PaymentStatus.pending;
      case 'completed':
        return PaymentStatus.completed;
      case 'cancelled':
        return PaymentStatus.cancelled;
      default:
        return PaymentStatus.completed;
    }
  }

  /// Convert to string (for database)
  String toDbString() {
    return name;
  }

  /// Get display text in Arabic
  String get displayText {
    switch (this) {
      case PaymentStatus.pending:
        return 'في الانتظار';
      case PaymentStatus.completed:
        return 'مكتمل';
      case PaymentStatus.cancelled:
        return 'ملغي';
    }
  }
}

/// Payment model for دائن مدين (Creditor-Debtor) system
class Payment {
  final String id;
  final String debtId;
  final String customerId;
  final String businessOwnerId;
  final double amount;
  final DateTime paymentDate;
  final PaymentStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Payment({
    required this.id,
    required this.debtId,
    required this.customerId,
    required this.businessOwnerId,
    required this.amount,
    required this.paymentDate,
    required this.status,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Payment from JSON (from Supabase)
  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'] as String,
      debtId: json['debt_id'] as String,
      customerId: json['customer_id'] as String,
      businessOwnerId: json['business_owner_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      paymentDate: DateTime.parse(json['payment_date'] as String),
      status: PaymentStatus.fromString(json['status'] as String? ?? 'completed'),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert Payment to JSON (for Supabase)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'debt_id': debtId,
      'customer_id': customerId,
      'business_owner_id': businessOwnerId,
      'amount': amount,
      'payment_date': paymentDate.toIso8601String(),
      'status': status.toDbString(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a new Payment for insertion (without timestamps)
  Map<String, dynamic> toInsertJson() {
    return {
      'debt_id': debtId,
      'customer_id': customerId,
      'business_owner_id': businessOwnerId,
      'amount': amount,
      'payment_date': paymentDate.toIso8601String(),
      'status': status.toDbString(),
      'notes': notes,
    };
  }

  /// Create a copy with updated fields
  Payment copyWith({
    String? id,
    String? debtId,
    String? customerId,
    String? businessOwnerId,
    double? amount,
    DateTime? paymentDate,
    PaymentStatus? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Payment(
      id: id ?? this.id,
      debtId: debtId ?? this.debtId,
      customerId: customerId ?? this.customerId,
      businessOwnerId: businessOwnerId ?? this.businessOwnerId,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create a Payment for new entry
  factory Payment.create({
    required String debtId,
    required String customerId,
    required String businessOwnerId,
    required double amount,
    DateTime? paymentDate,
    PaymentStatus status = PaymentStatus.completed,
    String? notes,
  }) {
    final now = DateTime.now();
    return Payment(
      id: const Uuid().v4(),
      debtId: debtId,
      customerId: customerId,
      businessOwnerId: businessOwnerId,
      amount: amount,
      paymentDate: paymentDate ?? now,
      status: status,
      notes: notes,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Mark payment as completed
  Payment markAsCompleted() {
    return copyWith(
      status: PaymentStatus.completed,
      updatedAt: DateTime.now(),
    );
  }

  /// Mark payment as cancelled
  Payment markAsCancelled() {
    return copyWith(
      status: PaymentStatus.cancelled,
      updatedAt: DateTime.now(),
    );
  }

  /// Get formatted amount string
  String get formattedAmount {
    return amount.toStringAsFixed(2);
  }

  /// Check if payment is completed
  bool get isCompleted => status == PaymentStatus.completed;

  /// Check if payment is pending
  bool get isPending => status == PaymentStatus.pending;

  /// Check if payment is cancelled
  bool get isCancelled => status == PaymentStatus.cancelled;

  @override
  String toString() {
    return 'Payment(id: $id, amount: $amount, status: $status, paymentDate: $paymentDate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Payment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
