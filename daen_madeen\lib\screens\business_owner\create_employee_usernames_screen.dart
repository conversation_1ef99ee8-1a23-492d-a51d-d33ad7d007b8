import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';

/// Create employee usernames screen for business owners
class CreateEmployeeUsernamesScreen extends StatefulWidget {
  const CreateEmployeeUsernamesScreen({super.key});

  @override
  State<CreateEmployeeUsernamesScreen> createState() => _CreateEmployeeUsernamesScreenState();
}

class _CreateEmployeeUsernamesScreenState extends State<CreateEmployeeUsernamesScreen> {
  List<Employee> _employees = [];
  List<Employee> _filteredEmployees = [];
  bool _isLoading = false;
  String? _errorMessage;
  final TextEditingController _searchController = TextEditingController();
  final Map<String, TextEditingController> _usernameControllers = {};
  final Map<String, TextEditingController> _passwordControllers = {};

  @override
  void initState() {
    super.initState();
    _loadEmployees();
    _searchController.addListener(_filterEmployees);
  }

  @override
  void dispose() {
    _searchController.dispose();
    for (final controller in _usernameControllers.values) {
      controller.dispose();
    }
    for (final controller in _passwordControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadEmployees() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final userProfile = authProvider.userProfile;
      
      if (userProfile == null || userProfile is! BusinessOwner) {
        throw Exception('لم يتم العثور على بيانات صاحب العمل');
      }
      
      final businessOwner = userProfile as BusinessOwner;
      final employeesWithoutUsernames = await EmployeeService.getEmployeesWithoutUsernames(businessOwner.id);

      setState(() {
        _employees = employeesWithoutUsernames;
        _filteredEmployees = employeesWithoutUsernames;
        _isLoading = false;
      });

      // Initialize controllers
      for (final employee in _employees) {
        _usernameControllers[employee.id] = TextEditingController();
        _passwordControllers[employee.id] = TextEditingController(text: '123456');
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل الموظفين: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  void _filterEmployees() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredEmployees = _employees.where((employee) {
        return employee.name.toLowerCase().contains(query) ||
               employee.position.toLowerCase().contains(query);
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء أسماء مستخدمين للموظفين'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Info header
          Container(
            width: double.infinity,
            color: Colors.blue.shade50,
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.info,
                  color: Colors.blue.shade700,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'إنشاء أسماء مستخدمين للموظفين للدخول إلى النظام',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                ),
              ],
            ),
          ),

          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث عن موظف...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

          // Employees list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 64,
                              color: Colors.red.shade400,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage!,
                              style: TextStyle(
                                color: Colors.red.shade600,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadEmployees,
                              child: const Text('إعادة المحاولة'),
                            ),
                          ],
                        ),
                      )
                    : _filteredEmployees.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.groups_outlined,
                                  size: 64,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _employees.isEmpty
                                      ? 'جميع الموظفين لديهم أسماء مستخدمين بالفعل'
                                      : 'لا توجد نتائج للبحث',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: _filteredEmployees.length,
                            itemBuilder: (context, index) {
                              final employee = _filteredEmployees[index];
                              return _buildEmployeeUsernameCard(employee);
                            },
                          ),
          ),

          // Create all usernames button
          if (_filteredEmployees.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: ElevatedButton(
                onPressed: _createAllUsernames,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF27AE60),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'إنشاء أسماء المستخدمين لجميع الموظفين',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmployeeUsernameCard(Employee employee) {
    final usernameController = _usernameControllers[employee.id]!;
    final passwordController = _passwordControllers[employee.id]!;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Employee info
            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: Colors.blue.shade100,
                  child: Icon(
                    Icons.person,
                    color: Colors.blue.shade700,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        employee.position,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Username field
            TextField(
              controller: usernameController,
              textDirection: TextDirection.rtl,
              decoration: InputDecoration(
                labelText: 'اسم المستخدم',
                hintText: 'أدخل اسم المستخدم',
                hintTextDirection: TextDirection.rtl,
                prefixIcon: const Icon(Icons.person),
                border: const OutlineInputBorder(),
                isDense: true,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Password field
            TextField(
              controller: passwordController,
              textDirection: TextDirection.rtl,
              decoration: InputDecoration(
                labelText: 'كلمة المرور',
                hintText: 'أدخل كلمة المرور',
                hintTextDirection: TextDirection.rtl,
                prefixIcon: const Icon(Icons.lock),
                border: const OutlineInputBorder(),
                isDense: true,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Create username button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _createUsernameForEmployee(employee),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('إنشاء اسم المستخدم'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _createUsernameForEmployee(Employee employee) async {
    final usernameController = _usernameControllers[employee.id]!;
    final passwordController = _passwordControllers[employee.id]!;
    
    final username = usernameController.text.trim();
    final password = passwordController.text.trim();
    
    if (username.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال اسم المستخدم'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    
    if (password.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال كلمة المرور'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final success = await EmployeeService.createEmployeeUsername(
        employeeId: employee.id,
        username: username,
        password: password,
      );

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء اسم المستخدم "${username}" للموظف ${employee.name}'),
            backgroundColor: Colors.green,
          ),
        );

        // Remove employee from list after successful creation
        setState(() {
          _employees.removeWhere((e) => e.id == employee.id);
          _filteredEmployees.removeWhere((e) => e.id == employee.id);
        });
      } else {
        throw Exception('فشل في إنشاء اسم المستخدم');
      }

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إنشاء اسم المستخدم: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _createAllUsernames() async {
    final employeeCredentials = <String, Map<String, String>>{};
    bool hasEmptyFields = false;

    // Collect all credentials
    for (final employee in _filteredEmployees) {
      final usernameController = _usernameControllers[employee.id]!;
      final passwordController = _passwordControllers[employee.id]!;

      final username = usernameController.text.trim();
      final password = passwordController.text.trim();

      if (username.isEmpty || password.isEmpty) {
        hasEmptyFields = true;
        continue;
      }

      employeeCredentials[employee.id] = {
        'username': username,
        'password': password,
      };
    }

    if (hasEmptyFields && employeeCredentials.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final results = await EmployeeService.createMultipleEmployeeUsernames(
        employeeCredentials: employeeCredentials,
      );

      final successCount = results.values.where((success) => success).length;
      final totalCount = results.length;

      if (successCount == totalCount) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء أسماء المستخدمين لجميع الموظفين ($successCount) بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (successCount > 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء $successCount من أصل $totalCount أسماء مستخدمين'),
            backgroundColor: Colors.orange,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في إنشاء أسماء المستخدمين'),
            backgroundColor: Colors.red,
          ),
        );
      }

      _loadEmployees(); // Refresh the list
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إنشاء أسماء المستخدمين: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
