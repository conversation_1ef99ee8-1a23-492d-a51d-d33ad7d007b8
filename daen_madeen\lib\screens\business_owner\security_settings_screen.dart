import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/local_storage_service.dart';

class SecuritySettingsScreen extends StatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  State<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends State<SecuritySettingsScreen> {
  bool _enableAppLock = false;
  bool _enableBiometric = false;
  bool _enableAutoLogout = true;
  bool _enableDataEncryption = true;
  int _autoLogoutMinutes = 30;
  String _lockType = 'pin'; // pin, pattern, biometric

  @override
  void initState() {
    super.initState();
    _loadSecuritySettings();
  }

  Future<void> _loadSecuritySettings() async {
    setState(() {
      _enableAppLock = LocalStorageService.getSetting('enable_app_lock', defaultValue: false);
      _enableBiometric = LocalStorageService.getSetting('enable_biometric', defaultValue: false);
      _enableAutoLogout = LocalStorageService.getSetting('enable_auto_logout', defaultValue: true);
      _enableDataEncryption = LocalStorageService.getSetting('enable_data_encryption', defaultValue: true);
      _autoLogoutMinutes = LocalStorageService.getSetting('auto_logout_minutes', defaultValue: 30);
      _lockType = LocalStorageService.getSetting('lock_type', defaultValue: 'pin');
    });
  }

  Future<void> _saveSetting(String key, dynamic value) async {
    await LocalStorageService.saveSetting(key, value);
    HapticFeedback.lightImpact();
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text(
          'إعدادات الأمان',
          style: TextStyle(
            fontWeight: FontWeight.bold, 
            fontSize: 22,
            color: Colors.white,
            letterSpacing: 0.5,
          ),
        ),
        backgroundColor: const Color(0xFFE53E3E),
        foregroundColor: Colors.white,
        elevation: 8,
        shadowColor: const Color(0xFFE53E3E).withOpacity(0.3),
        centerTitle: true,
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
            onPressed: () => Navigator.pop(context),
            iconSize: 20,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFFE53E3E), Color(0xFFFC8181)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFE53E3E), Color(0xFFFC8181)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFE53E3E).withOpacity(0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: const Column(
                children: [
                  Icon(
                    Icons.security,
                    size: 48,
                    color: Colors.white,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'حماية بياناتك',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'إعدادات الأمان والخصوصية المتقدمة',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                    textDirection: TextDirection.rtl,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // App Lock Section
            _buildSecurityCard(
              title: 'قفل التطبيق',
              icon: Icons.lock,
              color: const Color(0xFF3182CE),
              children: [
                _buildSwitchTile(
                  'تفعيل قفل التطبيق',
                  'حماية التطبيق برقم سري أو بصمة',
                  _enableAppLock,
                  (value) {
                    setState(() => _enableAppLock = value);
                    _saveSetting('enable_app_lock', value);
                    if (value) {
                      _showSetupLockDialog();
                    }
                  },
                ),
                if (_enableAppLock) ...[
                  _buildRadioTile(
                    'رقم سري (PIN)',
                    'pin',
                    _lockType,
                    (value) {
                      setState(() => _lockType = value!);
                      _saveSetting('lock_type', value);
                    },
                  ),
                  _buildRadioTile(
                    'نمط (Pattern)',
                    'pattern',
                    _lockType,
                    (value) {
                      setState(() => _lockType = value!);
                      _saveSetting('lock_type', value);
                    },
                  ),
                  _buildSwitchTile(
                    'البصمة الحيوية',
                    'استخدام بصمة الإصبع أو الوجه',
                    _enableBiometric,
                    (value) {
                      setState(() => _enableBiometric = value);
                      _saveSetting('enable_biometric', value);
                    },
                  ),
                ],
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Auto Logout Section
            _buildSecurityCard(
              title: 'تسجيل الخروج التلقائي',
              icon: Icons.timer,
              color: const Color(0xFFD69E2E),
              children: [
                _buildSwitchTile(
                  'تفعيل الخروج التلقائي',
                  'تسجيل خروج تلقائي عند عدم النشاط',
                  _enableAutoLogout,
                  (value) {
                    setState(() => _enableAutoLogout = value);
                    _saveSetting('enable_auto_logout', value);
                  },
                ),
                if (_enableAutoLogout)
                  _buildSliderTile(
                    'مدة عدم النشاط',
                    _autoLogoutMinutes.toDouble(),
                    5.0,
                    120.0,
                    (value) {
                      setState(() => _autoLogoutMinutes = value.round());
                      _saveSetting('auto_logout_minutes', _autoLogoutMinutes);
                    },
                    '${_autoLogoutMinutes} دقيقة',
                  ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Data Protection Section
            _buildSecurityCard(
              title: 'حماية البيانات',
              icon: Icons.enhanced_encryption,
              color: const Color(0xFF38A169),
              children: [
                _buildSwitchTile(
                  'تشفير البيانات',
                  'تشفير البيانات المحفوظة محلياً',
                  _enableDataEncryption,
                  (value) {
                    setState(() => _enableDataEncryption = value);
                    _saveSetting('enable_data_encryption', value);
                  },
                ),
                _buildActionTile(
                  'تغيير كلمة المرور',
                  'تحديث كلمة مرور الحساب',
                  Icons.password,
                  onTap: () => _showChangePasswordDialog(),
                ),
                _buildActionTile(
                  'سجل الأنشطة',
                  'عرض سجل تسجيل الدخول والأنشطة',
                  Icons.history,
                  onTap: () => _showActivityLog(),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Emergency Section
            _buildSecurityCard(
              title: 'الطوارئ',
              icon: Icons.warning,
              color: const Color(0xFFE53E3E),
              children: [
                _buildActionTile(
                  'إعادة تعيين الأمان',
                  'إزالة جميع إعدادات الأمان',
                  Icons.restore,
                  color: Colors.red,
                  onTap: () => _showResetSecurityDialog(),
                ),
                _buildActionTile(
                  'تسجيل خروج من جميع الأجهزة',
                  'إنهاء جميع الجلسات النشطة',
                  Icons.logout,
                  color: Colors.red,
                  onTap: () => _showLogoutAllDialog(),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              textDirection: TextDirection.rtl,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
        textDirection: TextDirection.rtl,
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey.shade600),
        textDirection: TextDirection.rtl,
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFFE53E3E),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildRadioTile(
    String title,
    String value,
    String groupValue,
    ValueChanged<String?> onChanged,
  ) {
    return RadioListTile<String>(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
        textDirection: TextDirection.rtl,
      ),
      value: value,
      groupValue: groupValue,
      onChanged: onChanged,
      activeColor: const Color(0xFFE53E3E),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildSliderTile(
    String title,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
    String displayValue,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(fontWeight: FontWeight.w600),
                textDirection: TextDirection.rtl,
              ),
              Text(
                displayValue,
                style: const TextStyle(
                  color: Color(0xFFE53E3E),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Slider(
            value: value,
            min: min,
            max: max,
            onChanged: onChanged,
            activeColor: const Color(0xFFE53E3E),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon, {
    VoidCallback? onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color ?? Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
        textDirection: TextDirection.rtl,
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey.shade600),
        textDirection: TextDirection.rtl,
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  void _showSetupLockDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text(
          'إعداد قفل التطبيق',
          textDirection: TextDirection.rtl,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: const Text(
          'سيتم توجيهك لإعداد قفل التطبيق. اختر طريقة الحماية المناسبة لك.',
          textDirection: TextDirection.rtl,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showSnackBar('سيتم إعداد قفل التطبيق قريباً', Colors.blue);
            },
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }

  void _showChangePasswordDialog() {
    _showSnackBar('تغيير كلمة المرور سيكون متاحاً قريباً', Colors.blue);
  }

  void _showActivityLog() {
    _showSnackBar('سجل الأنشطة سيكون متاحاً قريباً', Colors.blue);
  }

  void _showResetSecurityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text(
          'إعادة تعيين الأمان',
          textDirection: TextDirection.rtl,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: const Text(
          'هل أنت متأكد من إزالة جميع إعدادات الأمان؟ هذا الإجراء لا يمكن التراجع عنه.',
          textDirection: TextDirection.rtl,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _resetSecuritySettings();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إعادة تعيين', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showLogoutAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text(
          'تسجيل خروج شامل',
          textDirection: TextDirection.rtl,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: const Text(
          'هل تريد تسجيل الخروج من جميع الأجهزة؟ ستحتاج لتسجيل الدخول مرة أخرى.',
          textDirection: TextDirection.rtl,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _logoutFromAllDevices();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تسجيل خروج', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _resetSecuritySettings() async {
    await LocalStorageService.deleteSetting('enable_app_lock');
    await LocalStorageService.deleteSetting('enable_biometric');
    await LocalStorageService.deleteSetting('enable_auto_logout');
    await LocalStorageService.deleteSetting('enable_data_encryption');
    await LocalStorageService.deleteSetting('auto_logout_minutes');
    await LocalStorageService.deleteSetting('lock_type');

    _loadSecuritySettings();
    _showSnackBar('تم إعادة تعيين إعدادات الأمان بنجاح', Colors.green);
  }

  Future<void> _logoutFromAllDevices() async {
    _showSnackBar('تم تسجيل الخروج من جميع الأجهزة', Colors.green);
  }
}
