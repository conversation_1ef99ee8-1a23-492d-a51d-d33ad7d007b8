# Important Setup Notes for دائن مدين App

## Before Running the App

### 1. Supabase Configuration Required

The app is currently configured with placeholder Supabase credentials. You need to:

1. **Create a Supabase Project:**
   - Go to https://supabase.com
   - Create a new project
   - Follow the setup guide in `SUPABASE_SETUP.md`

2. **Update Supabase Credentials:**
   - Open `lib/utils/supabase_config.dart`
   - Replace the placeholder values:
   ```dart
   static const String supabaseUrl = 'https://your-project-id.supabase.co';
   static const String supabaseAnonKey = 'your-anon-key-here';
   ```

3. **Run Database Setup:**
   - Execute the SQL scripts in `supabase_schema.sql`
   - Execute the RLS policies in `supabase_rls_policies.sql`

### 2. Running the App Without Supabase (Demo Mode)

If you want to run the app immediately for demonstration purposes, you can:

1. **Comment out Supabase initialization** in `lib/main.dart`:
   ```dart
   // await SupabaseConfig.initialize();
   ```

2. **Mock the authentication** by modifying the auth provider to skip Supabase calls

### 3. Current App Status

✅ **Working Features:**
- App structure and navigation
- UI components and screens
- Local storage with Hive
- Authentication flow (UI only)
- Dashboard layouts
- Data models and services

⚠️ **Requires Setup:**
- Supabase backend connection
- Real authentication
- Data synchronization
- Real-time notifications

### 4. Running the App

Once Supabase is configured:

```bash
flutter pub get
flutter run
```

### 5. Testing the App

The app has two user types:

1. **Business Owner Login:**
   - Email: (create in Supabase)
   - Password: (set in Supabase)

2. **Customer Login:**
   - Username: (created by business owner)
   - Password: (set by business owner)

### 6. Development Notes

- The app is designed for Arabic RTL layout
- All text is in Arabic
- Offline functionality is implemented with Hive
- Real-time features use Supabase subscriptions
- Row Level Security (RLS) is properly configured

### 7. Next Steps for Full Functionality

1. Set up Supabase project
2. Configure authentication
3. Test user registration and login
4. Implement remaining CRUD operations
5. Add real-time notifications
6. Test offline synchronization

## Support

If you need help with setup, refer to:
- `SUPABASE_SETUP.md` for backend setup
- Flutter documentation for mobile development
- Supabase documentation for backend features
