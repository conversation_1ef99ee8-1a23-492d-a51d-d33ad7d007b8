import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'utils/supabase_config.dart';
import 'utils/connection_checker.dart';
import 'providers/auth_provider.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/business_owner_registration_screen.dart';
import 'screens/auth/modern_registration_screen.dart';
import 'screens/auth/forgot_password_screen.dart';
import 'screens/auth/email_verification_screen.dart';
import 'screens/debug/connection_status_screen.dart';
import 'screens/business_owner/modern_dashboard_screen.dart';
import 'screens/business_owner/profile_screen.dart';
import 'screens/business_owner/backup_screen.dart';
import 'screens/business_owner/simple_debt_screen.dart';
import 'screens/business_owner/settings_screen.dart';
import 'screens/customer/customer_dashboard_screen.dart';
import 'screens/contact_us_screen.dart';
import 'screens/customer/notifications_screen.dart';
import 'screens/privacy_policy_screen.dart';
import 'screens/subscription_plans_screen.dart';
import 'services/local_storage_service.dart';
import 'services/saved_login_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive for local storage
  await Hive.initFlutter();
  await LocalStorageService.initialize();
  await SavedLoginService.instance.init();

  // Initialize demo employees data
  await LocalStorageService.initializeDemoEmployees();

  // Initialize Supabase (with error handling for demo)
  try {
    await SupabaseConfig.initialize();
    print('✅ Supabase initialized successfully!');
  } catch (e) {
    print('❌ Supabase initialization failed: $e');
    print('🔄 App will run in demo mode. Please configure Supabase for full functionality.');
  }

  // Print connection status report
  ConnectionChecker.printDetailedReport();

  runApp(const DaenMadeenApp());
}

class DaenMadeenApp extends StatelessWidget {
  const DaenMadeenApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'دائن مدين',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Arial',
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue.shade800,
            brightness: Brightness.light,
          ),
          textTheme: const TextTheme(
            bodyLarge: TextStyle(fontSize: 16, height: 1.5),
            bodyMedium: TextStyle(fontSize: 14, height: 1.4),
            titleLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            titleMedium: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
              shadowColor: Colors.black26,
            ),
          ),
          outlinedButtonTheme: OutlinedButtonThemeData(
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              side: const BorderSide(width: 1.5),
            ),
          ),
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.blue.shade800, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 1.5),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
          ),
          cardTheme: CardThemeData(
            elevation: 4,
            shadowColor: Colors.black12,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          appBarTheme: AppBarTheme(
            elevation: 0,
            centerTitle: true,
            titleTextStyle: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          snackBarTheme: SnackBarThemeData(
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentTextStyle: const TextStyle(fontSize: 14),
          ),
        ),
        home: const AuthWrapper(),
        routes: {
          '/login': (context) => const LoginScreen(),
          '/register': (context) => const BusinessOwnerRegistrationScreen(),
          '/modern_register': (context) => const ModernRegistrationScreen(),
          '/forgot_password': (context) => const ForgotPasswordScreen(),
          '/email_verification': (context) => const EmailVerificationScreen(),
          '/connection_status': (context) => const ConnectionStatusScreen(),
          '/business_dashboard': (context) => const ModernBusinessOwnerDashboardScreen(),
          '/customer_dashboard': (context) => const CustomerDashboardScreen(),
          '/contact_us': (context) => const ContactUsScreen(isBusinessOwner: false),
          '/contact_us_business': (context) => const ContactUsScreen(isBusinessOwner: true),
          '/notifications': (context) => const NotificationsScreen(),
          '/privacy_policy': (context) => const PrivacyPolicyScreen(),
          '/subscription_plans': (context) => const SubscriptionPlansScreen(),
          '/profile': (context) => const ProfileScreen(),
          '/backup': (context) => const BackupScreen(),
          '/add_debt': (context) => const SimpleDebtScreen(),
          '/settings': (context) => const SettingsScreen(),
        },
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    // Initialize auth provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AuthProvider>(context, listen: false).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        print('Main: Auth state = ${authProvider.state}');
        print('Main: isAuthenticated = ${authProvider.isAuthenticated}');
        print('Main: userType = ${authProvider.userType}');

        // Show loading screen while initializing
        if (authProvider.state == AppAuthState.initial ||
            authProvider.state == AppAuthState.loading) {
          print('Main: Showing loading screen');
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // Show appropriate screen based on auth state
        if (authProvider.isAuthenticated) {
          print('Main: User is authenticated');
          if (authProvider.isBusinessOwner) {
            print('Main: Showing business owner dashboard');
            return const ModernBusinessOwnerDashboardScreen();
          } else if (authProvider.isCustomer) {
            print('Main: Showing customer dashboard');
            return const CustomerDashboardScreen();
          }
        }

        // Show login screen if not authenticated
        print('Main: Showing login screen');
        return const LoginScreen();
      },
    );
  }
}
