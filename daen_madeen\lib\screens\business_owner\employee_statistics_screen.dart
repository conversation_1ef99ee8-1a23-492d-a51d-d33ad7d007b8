import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';

/// Employee statistics screen for business owners
class EmployeeStatisticsScreen extends StatefulWidget {
  const EmployeeStatisticsScreen({super.key});

  @override
  State<EmployeeStatisticsScreen> createState() => _EmployeeStatisticsScreenState();
}

class _EmployeeStatisticsScreenState extends State<EmployeeStatisticsScreen> {
  List<Employee> _employees = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Statistics data
  int _totalEmployees = 0;
  int _activeEmployees = 0;
  int _inactiveEmployees = 0;
  double _totalSalaries = 0.0;
  double _averageSalary = 0.0;
  Map<String, int> _positionCounts = {};
  Map<String, double> _positionSalaries = {};
  Employee? _highestPaidEmployee;
  Employee? _lowestPaidEmployee;
  Employee? _longestServingEmployee;
  Employee? _newestEmployee;

  @override
  void initState() {
    super.initState();
    _loadEmployeeStatistics();
  }

  Future<void> _loadEmployeeStatistics() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final userProfile = authProvider.userProfile;
      
      if (userProfile == null || userProfile is! BusinessOwner) {
        throw Exception('لم يتم العثور على بيانات صاحب العمل');
      }
      
      final businessOwner = userProfile as BusinessOwner;
      final employees = await EmployeeService.getEmployeesByBusinessOwner(businessOwner.id);

      setState(() {
        _employees = employees;
        _calculateStatistics();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل الإحصائيات: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  void _calculateStatistics() {
    _totalEmployees = _employees.length;
    _activeEmployees = _employees.where((e) => e.isActive).length;
    _inactiveEmployees = _totalEmployees - _activeEmployees;
    
    // Calculate salary statistics
    final activeEmployees = _employees.where((e) => e.isActive).toList();
    _totalSalaries = activeEmployees.fold(0.0, (sum, e) => sum + e.salary);
    _averageSalary = activeEmployees.isNotEmpty ? _totalSalaries / activeEmployees.length : 0.0;
    
    // Position statistics
    _positionCounts.clear();
    _positionSalaries.clear();
    
    for (final employee in activeEmployees) {
      _positionCounts[employee.position] = (_positionCounts[employee.position] ?? 0) + 1;
      _positionSalaries[employee.position] = (_positionSalaries[employee.position] ?? 0.0) + employee.salary;
    }
    
    // Find highest and lowest paid employees
    if (activeEmployees.isNotEmpty) {
      _highestPaidEmployee = activeEmployees.reduce((a, b) => a.salary > b.salary ? a : b);
      _lowestPaidEmployee = activeEmployees.reduce((a, b) => a.salary < b.salary ? a : b);
      
      // Find longest serving and newest employees
      _longestServingEmployee = activeEmployees.reduce((a, b) => a.hireDate.isBefore(b.hireDate) ? a : b);
      _newestEmployee = activeEmployees.reduce((a, b) => a.hireDate.isAfter(b.hireDate) ? a : b);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إحصائيات الموظفين'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadEmployeeStatistics,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Colors.red.shade600,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadEmployeeStatistics,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadEmployeeStatistics,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Overview statistics
                        _buildOverviewSection(),
                        const SizedBox(height: 20),
                        
                        // Salary statistics
                        _buildSalarySection(),
                        const SizedBox(height: 20),
                        
                        // Position statistics
                        _buildPositionSection(),
                        const SizedBox(height: 20),
                        
                        // Employee highlights
                        _buildHighlightsSection(),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildOverviewSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.groups, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 8),
                Text(
                  'نظرة عامة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الموظفين',
                    _totalEmployees.toString(),
                    Icons.people,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'الموظفين النشطين',
                    _activeEmployees.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'الموظفين غير النشطين',
                    _inactiveEmployees.toString(),
                    Icons.cancel,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'المناصب المختلفة',
                    _positionCounts.length.toString(),
                    Icons.work,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalarySection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money, color: Colors.green.shade700, size: 24),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات الرواتب',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الرواتب',
                    '${_totalSalaries.toStringAsFixed(0)} ر.س',
                    Icons.account_balance_wallet,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'متوسط الراتب',
                    '${_averageSalary.toStringAsFixed(0)} ر.س',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPositionSection() {
    if (_positionCounts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.work_outline, color: Colors.purple.shade700, size: 24),
                const SizedBox(width: 8),
                Text(
                  'توزيع المناصب',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._positionCounts.entries.map((entry) {
              final position = entry.key;
              final count = entry.value;
              final totalSalary = _positionSalaries[position] ?? 0.0;
              final averageSalary = totalSalary / count;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            position,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            textDirection: TextDirection.rtl,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'متوسط الراتب: ${averageSalary.toStringAsFixed(0)} ر.س',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                            textDirection: TextDirection.rtl,
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade100,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        '$count موظف',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildHighlightsSection() {
    if (_employees.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber.shade700, size: 24),
                const SizedBox(width: 8),
                Text(
                  'أبرز الموظفين',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.amber.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_highestPaidEmployee != null)
              _buildHighlightCard(
                'أعلى راتب',
                _highestPaidEmployee!.name,
                _highestPaidEmployee!.position,
                '${_highestPaidEmployee!.salary.toStringAsFixed(0)} ر.س',
                Icons.trending_up,
                Colors.green,
              ),
            if (_lowestPaidEmployee != null)
              _buildHighlightCard(
                'أقل راتب',
                _lowestPaidEmployee!.name,
                _lowestPaidEmployee!.position,
                '${_lowestPaidEmployee!.salary.toStringAsFixed(0)} ر.س',
                Icons.trending_down,
                Colors.orange,
              ),
            if (_longestServingEmployee != null)
              _buildHighlightCard(
                'أطول خدمة',
                _longestServingEmployee!.name,
                _longestServingEmployee!.position,
                _longestServingEmployee!.formattedEmploymentDuration,
                Icons.access_time,
                Colors.blue,
              ),
            if (_newestEmployee != null)
              _buildHighlightCard(
                'أحدث موظف',
                _newestEmployee!.name,
                _newestEmployee!.position,
                _newestEmployee!.formattedHireDate,
                Icons.new_releases,
                Colors.purple,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  Widget _buildHighlightCard(
    String category,
    String name,
    String position,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                  textDirection: TextDirection.rtl,
                ),
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textDirection: TextDirection.rtl,
                ),
                Text(
                  position,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
    );
  }
}
