import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> with SingleTickerProviderStateMixin {
  List<NotificationItem> notifications = [];
  bool _isLoading = true;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    // Simulate loading delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Generate demo notifications
    setState(() {
      notifications = _generateDemoNotifications();
      _isLoading = false;
    });
  }

  List<NotificationItem> _generateDemoNotifications() {
    final now = DateTime.now();
    return [
      NotificationItem(
        id: '1',
        title: 'دفعة جديدة من أحمد محمد',
        message: 'تم استلام دفعة بقيمة 500 ريال من العميل أحمد محمد',
        type: NotificationType.payment,
        timestamp: now.subtract(const Duration(minutes: 5)),
        isRead: false,
      ),
      NotificationItem(
        id: '2',
        title: 'عميل جديد مضاف',
        message: 'تم إضافة العميل سارة أحمد بنجاح إلى قائمة العملاء',
        type: NotificationType.customer,
        timestamp: now.subtract(const Duration(hours: 2)),
        isRead: false,
      ),
      NotificationItem(
        id: '3',
        title: 'تذكير: دين مستحق',
        message: 'العميل محمد علي لديه دين مستحق بقيمة 1200 ريال منذ أسبوع',
        type: NotificationType.reminder,
        timestamp: now.subtract(const Duration(hours: 6)),
        isRead: true,
      ),
      NotificationItem(
        id: '4',
        title: 'تحديث النظام',
        message: 'تم تحديث التطبيق إلى الإصدار الجديد بنجاح',
        type: NotificationType.system,
        timestamp: now.subtract(const Duration(days: 1)),
        isRead: true,
      ),
      NotificationItem(
        id: '5',
        title: 'دفعة جديدة من فاطمة سالم',
        message: 'تم استلام دفعة بقيمة 300 ريال من العميلة فاطمة سالم',
        type: NotificationType.payment,
        timestamp: now.subtract(const Duration(days: 2)),
        isRead: true,
      ),
      NotificationItem(
        id: '6',
        title: 'تنبيه أمان',
        message: 'تم تسجيل دخول جديد إلى حسابك من جهاز غير معروف',
        type: NotificationType.security,
        timestamp: now.subtract(const Duration(days: 3)),
        isRead: true,
      ),
    ];
  }

  void _markAsRead(String notificationId) {
    setState(() {
      final index = notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        notifications[index] = notifications[index].copyWith(isRead: true);
      }
    });
  }

  void _markAllAsRead() {
    setState(() {
      notifications = notifications.map((n) => n.copyWith(isRead: true)).toList();
    });
  }

  void _deleteNotification(String notificationId) {
    setState(() {
      notifications.removeWhere((n) => n.id == notificationId);
    });
  }

  List<NotificationItem> get systemNotifications {
    return notifications.where((n) => n.type == NotificationType.system || n.type == NotificationType.security).toList();
  }

  List<NotificationItem> get customerNotifications {
    return notifications.where((n) => n.type == NotificationType.customer || n.type == NotificationType.payment).toList();
  }

  List<NotificationItem> get reminderNotifications {
    return notifications.where((n) => n.type == NotificationType.reminder).toList();
  }

  @override
  Widget build(BuildContext context) {
    final unreadCount = notifications.where((n) => !n.isRead).length;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: const Text(
          'الإشعارات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: const Color(0xFF0A0E27),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          if (unreadCount > 0)
            TextButton(
              onPressed: _markAllAsRead,
              child: const Text(
                'قراءة الكل',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
          tabs: [
            Tab(
              text: 'تنبيهات النظام',
              icon: Icon(Icons.settings, size: 20),
            ),
            Tab(
              text: 'العملاء والدفعات',
              icon: Icon(Icons.people, size: 20),
            ),
            Tab(
              text: 'التذكيرات',
              icon: Icon(Icons.schedule, size: 20),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF0A0E27),
              ),
            )
          : Column(
              children: [
                if (unreadCount > 0)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    color: Colors.blue.shade50,
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.blue.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'لديك $unreadCount إشعار غير مقروء',
                          style: TextStyle(
                            color: Colors.blue.shade800,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildNotificationsList(systemNotifications, 'تنبيهات النظام'),
                      _buildNotificationsList(customerNotifications, 'العملاء والدفعات'),
                      _buildNotificationsList(reminderNotifications, 'التذكيرات'),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildNotificationsList(List<NotificationItem> notificationsList, String categoryName) {
    if (notificationsList.isEmpty) {
      return _buildEmptyState(categoryName);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: notificationsList.length,
      itemBuilder: (context, index) {
        final notification = notificationsList[index];
        return _buildNotificationCard(notification);
      },
    );
  }

  Widget _buildEmptyState(String categoryName) {
    IconData icon;
    String message;

    switch (categoryName) {
      case 'تنبيهات النظام':
        icon = Icons.settings;
        message = 'لا توجد تنبيهات نظام';
        break;
      case 'العملاء والدفعات':
        icon = Icons.people;
        message = 'لا توجد إشعارات عملاء أو دفعات';
        break;
      case 'التذكيرات':
        icon = Icons.schedule;
        message = 'لا توجد تذكيرات';
        break;
      default:
        icon = Icons.notifications_off_outlined;
        message = 'لا توجد إشعارات';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              icon,
              size: 60,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            message,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر الإشعارات الجديدة هنا',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(NotificationItem notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: notification.isRead ? Colors.white : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification.isRead ? Colors.grey.shade200 : Colors.blue.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: _getNotificationColor(notification.type).withOpacity(0.1),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Icon(
            _getNotificationIcon(notification.type),
            color: _getNotificationColor(notification.type),
            size: 24,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.bold,
                  color: const Color(0xFF1F2937),
                ),
              ),
            ),
            if (!notification.isRead)
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.blue.shade600,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              notification.message,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _formatTimestamp(notification.timestamp),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'mark_read' && !notification.isRead) {
              _markAsRead(notification.id);
            } else if (value == 'delete') {
              _deleteNotification(notification.id);
            }
          },
          itemBuilder: (context) => [
            if (!notification.isRead)
              const PopupMenuItem(
                value: 'mark_read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read, size: 20),
                    SizedBox(width: 8),
                    Text('تحديد كمقروء'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          child: Icon(
            Icons.more_vert,
            color: Colors.grey.shade400,
          ),
        ),
        onTap: () {
          if (!notification.isRead) {
            _markAsRead(notification.id);
          }
        },
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.customer:
        return Icons.person_add;
      case NotificationType.reminder:
        return Icons.schedule;
      case NotificationType.system:
        return Icons.system_update;
      case NotificationType.security:
        return Icons.security;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.customer:
        return Colors.blue;
      case NotificationType.reminder:
        return Colors.orange;
      case NotificationType.system:
        return Colors.purple;
      case NotificationType.security:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return DateFormat('dd/MM/yyyy').format(timestamp);
    }
  }
}

enum NotificationType {
  payment,
  customer,
  reminder,
  system,
  security,
}

class NotificationItem {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final bool isRead;

  NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    required this.isRead,
  });

  NotificationItem copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    bool? isRead,
  }) {
    return NotificationItem(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
    );
  }
}
