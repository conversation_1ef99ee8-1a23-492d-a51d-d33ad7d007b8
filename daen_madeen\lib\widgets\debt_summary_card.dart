import 'package:flutter/material.dart';
import '../models/models.dart';

/// Debt summary card widget for customer dashboard
class DebtSummaryCard extends StatelessWidget {
  final Customer customer;
  final List<Debt> debts;

  const DebtSummaryCard({
    super.key,
    required this.customer,
    required this.debts,
  });

  @override
  Widget build(BuildContext context) {
    final unpaidDebts = debts.where((debt) => !debt.isPaid).toList();
    final paidDebts = debts.where((debt) => debt.isPaid).toList();
    final totalUnpaidAmount = unpaidDebts.fold<double>(0, (sum, debt) => sum + debt.amount);
    final totalPaidAmount = paidDebts.fold<double>(0, (sum, debt) => sum + debt.amount);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص الحساب',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Current balance
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: customer.currentBalance > 0 
                    ? Colors.red.shade50 
                    : Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: customer.currentBalance > 0 
                      ? Colors.red.shade200 
                      : Colors.green.shade200,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    'الرصيد الحالي',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${customer.currentBalance.toStringAsFixed(2)} ريال',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: customer.currentBalance > 0 
                          ? Colors.red.shade700 
                          : Colors.green.shade700,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Credit limit and usage
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    label: 'الحد الائتماني',
                    value: '${customer.creditLimit.toStringAsFixed(2)} ريال',
                    color: Colors.blue.shade600,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildInfoItem(
                    label: 'المتاح',
                    value: '${customer.remainingCredit.toStringAsFixed(2)} ريال',
                    color: customer.remainingCredit > 0 
                        ? Colors.green.shade600 
                        : Colors.red.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Credit utilization bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'استخدام الائتمان',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      customer.creditLimit > 0
                          ? '${((customer.currentBalance / customer.creditLimit) * 100).toStringAsFixed(1)}%'
                          : '0%',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: customer.creditLimit > 0
                      ? (customer.currentBalance / customer.creditLimit).clamp(0.0, 1.0)
                      : 0.0,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    customer.hasExceededCreditLimit
                        ? Colors.red.shade600
                        : customer.currentBalance > (customer.creditLimit * 0.8)
                            ? Colors.orange.shade600
                            : Colors.green.shade600,
                  ),
                ),
              ],
            ),
            
            // Warning if over limit
            if (customer.hasExceededCreditLimit) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade300),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: Colors.red.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'لقد تجاوزت الحد الائتماني المسموح. يرجى سداد المبلغ المستحق.',
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Statistics
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    label: 'إجمالي الديون',
                    value: '${debts.length}',
                    color: Colors.blue.shade600,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatItem(
                    label: 'غير مدفوع',
                    value: '${unpaidDebts.length}',
                    color: Colors.orange.shade600,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatItem(
                    label: 'مدفوع',
                    value: '${paidDebts.length}',
                    color: Colors.green.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem({
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
