import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/supabase_config.dart';
import '../models/models.dart';
import '../services/local_storage_service.dart';
import '../services/connectivity_service.dart';

/// Payment service for دائن مدين (Creditor-Debtor) system
/// Handles payment CRUD operations with offline support
class PaymentService {
  static final SupabaseClient _client = SupabaseConfig.client;
  static final ConnectivityService _connectivity = ConnectivityService();

  /// Get all payments for a debt
  static Future<List<Payment>> getPaymentsByDebt(String debtId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.payments
            .select()
            .eq('debt_id', debtId)
            .order('payment_date', ascending: false);

        final payments = response.map((json) => Payment.fromJson(json)).toList();

        // Save to local storage
        for (final payment in payments) {
          await LocalStorageService.savePayment(payment);
        }

        return payments;
      } else {
        // Fetch from local storage
        final hivePayments = await LocalStorageService.getPaymentsByDebt(debtId);
        return hivePayments.map((hive) => Payment(
          id: hive.id,
          debtId: hive.debtId,
          customerId: hive.customerId,
          businessOwnerId: hive.businessOwnerId,
          amount: hive.amount,
          paymentDate: hive.paymentDate,
          status: PaymentStatus.fromString(hive.status),
          notes: hive.notes,
          createdAt: hive.createdAt,
          updatedAt: hive.updatedAt,
        )).toList();
      }
    } catch (e) {
      // Fallback to local storage
      final hivePayments = await LocalStorageService.getPaymentsByDebt(debtId);
      return hivePayments.map((hive) => Payment(
        id: hive.id,
        debtId: hive.debtId,
        customerId: hive.customerId,
        businessOwnerId: hive.businessOwnerId,
        amount: hive.amount,
        paymentDate: hive.paymentDate,
        status: PaymentStatus.fromString(hive.status),
        notes: hive.notes,
        createdAt: hive.createdAt,
        updatedAt: hive.updatedAt,
      )).toList();
    }
  }

  /// Get all payments for a customer
  static Future<List<Payment>> getPaymentsByCustomer(String customerId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.payments
            .select()
            .eq('customer_id', customerId)
            .order('payment_date', ascending: false);

        final payments = response.map((json) => Payment.fromJson(json)).toList();

        // Save to local storage
        for (final payment in payments) {
          await LocalStorageService.savePayment(payment);
        }

        return payments;
      } else {
        // Fetch from local storage
        final hivePayments = await LocalStorageService.getPaymentsByCustomer(customerId);
        return hivePayments.map((hive) => Payment(
          id: hive.id,
          debtId: hive.debtId,
          customerId: hive.customerId,
          businessOwnerId: hive.businessOwnerId,
          amount: hive.amount,
          paymentDate: hive.paymentDate,
          status: PaymentStatus.fromString(hive.status),
          notes: hive.notes,
          createdAt: hive.createdAt,
          updatedAt: hive.updatedAt,
        )).toList();
      }
    } catch (e) {
      // Fallback to local storage
      final hivePayments = await LocalStorageService.getPaymentsByCustomer(customerId);
      return hivePayments.map((hive) => Payment(
        id: hive.id,
        debtId: hive.debtId,
        customerId: hive.customerId,
        businessOwnerId: hive.businessOwnerId,
        amount: hive.amount,
        paymentDate: hive.paymentDate,
        status: PaymentStatus.fromString(hive.status),
        notes: hive.notes,
        createdAt: hive.createdAt,
        updatedAt: hive.updatedAt,
      )).toList();
    }
  }

  /// Get payment by ID
  static Future<Payment?> getPaymentById(String paymentId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.payments
            .select()
            .eq('id', paymentId)
            .maybeSingle();

        if (response != null) {
          final payment = Payment.fromJson(response);
          await LocalStorageService.savePayment(payment);
          return payment;
        }
      } else {
        // Fetch from local storage
        final hivePayment = await LocalStorageService.getPayment(paymentId);
        if (hivePayment != null) {
          return Payment(
            id: hivePayment.id,
            debtId: hivePayment.debtId,
            customerId: hivePayment.customerId,
            businessOwnerId: hivePayment.businessOwnerId,
            amount: hivePayment.amount,
            paymentDate: hivePayment.paymentDate,
            status: PaymentStatus.fromString(hivePayment.status),
            notes: hivePayment.notes,
            createdAt: hivePayment.createdAt,
            updatedAt: hivePayment.updatedAt,
          );
        }
      }
      return null;
    } catch (e) {
      // Fallback to local storage
      final hivePayment = await LocalStorageService.getPayment(paymentId);
      if (hivePayment != null) {
        return Payment(
          id: hivePayment.id,
          debtId: hivePayment.debtId,
          customerId: hivePayment.customerId,
          businessOwnerId: hivePayment.businessOwnerId,
          amount: hivePayment.amount,
          paymentDate: hivePayment.paymentDate,
          status: PaymentStatus.fromString(hivePayment.status),
          notes: hivePayment.notes,
          createdAt: hivePayment.createdAt,
          updatedAt: hivePayment.updatedAt,
        );
      }
      return null;
    }
  }

  /// Create a new payment
  static Future<Payment?> createPayment({
    required String debtId,
    required String customerId,
    required String businessOwnerId,
    required double amount,
    DateTime? paymentDate,
    PaymentStatus status = PaymentStatus.completed,
    String? notes,
  }) async {
    try {
      if (amount <= 0) {
        throw Exception('مبلغ الدفعة يجب أن يكون أكبر من صفر');
      }

      final payment = Payment.create(
        debtId: debtId,
        customerId: customerId,
        businessOwnerId: businessOwnerId,
        amount: amount,
        paymentDate: paymentDate,
        status: status,
        notes: notes,
      );

      if (_connectivity.isOnline) {
        // Insert to Supabase
        final response = await _client.payments
            .insert(payment.toInsertJson())
            .select()
            .single();

        final createdPayment = Payment.fromJson(response);
        await LocalStorageService.savePayment(createdPayment);
        return createdPayment;
      } else {
        // Save to local storage only (will sync later)
        await LocalStorageService.savePayment(payment, isSynced: false);
        return payment;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Update payment
  static Future<Payment?> updatePayment(Payment payment) async {
    try {
      if (_connectivity.isOnline) {
        // Update in Supabase
        final response = await _client.payments
            .update(payment.toJson())
            .eq('id', payment.id)
            .select()
            .single();

        final updatedPayment = Payment.fromJson(response);
        await LocalStorageService.savePayment(updatedPayment);
        return updatedPayment;
      } else {
        // Update in local storage only (will sync later)
        await LocalStorageService.savePayment(payment, isSynced: false);
        return payment;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Mark payment as completed
  static Future<Payment?> markPaymentAsCompleted(String paymentId) async {
    try {
      final payment = await getPaymentById(paymentId);
      if (payment != null && !payment.isCompleted) {
        final updatedPayment = payment.markAsCompleted();
        return await updatePayment(updatedPayment);
      }
      return payment;
    } catch (e) {
      return null;
    }
  }

  /// Mark payment as cancelled
  static Future<Payment?> markPaymentAsCancelled(String paymentId) async {
    try {
      final payment = await getPaymentById(paymentId);
      if (payment != null && !payment.isCancelled) {
        final updatedPayment = payment.markAsCancelled();
        return await updatePayment(updatedPayment);
      }
      return payment;
    } catch (e) {
      return null;
    }
  }

  /// Delete payment
  static Future<bool> deletePayment(String paymentId) async {
    try {
      if (_connectivity.isOnline) {
        // Delete from Supabase
        await _client.payments.delete().eq('id', paymentId);
      }
      
      // Delete from local storage
      await LocalStorageService.deletePayment(paymentId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get total payments for a debt
  static Future<double> getTotalPaymentsForDebt(String debtId) async {
    try {
      final payments = await getPaymentsByDebt(debtId);
      final completedPayments = payments.where((p) => p.isCompleted);
      return completedPayments.fold<double>(0, (sum, p) => sum + p.amount);
    } catch (e) {
      return 0.0;
    }
  }

  /// Get remaining debt amount after payments
  static Future<double> getRemainingDebtAmount(String debtId, double originalAmount) async {
    try {
      final totalPaid = await getTotalPaymentsForDebt(debtId);
      return originalAmount - totalPaid;
    } catch (e) {
      return originalAmount;
    }
  }

  /// Get all payments for a business owner
  static Future<List<Payment>> getPaymentsByBusinessOwner(String businessOwnerId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.payments
            .select()
            .eq('business_owner_id', businessOwnerId)
            .order('payment_date', ascending: false);

        final payments = response.map((json) => Payment.fromJson(json)).toList();

        // Save to local storage
        for (final payment in payments) {
          await LocalStorageService.savePayment(payment);
        }

        return payments;
      } else {
        // Fetch from local storage
        final hivePayments = await LocalStorageService.getPaymentsByBusinessOwner(businessOwnerId);
        return hivePayments.map((hive) => Payment(
          id: hive.id,
          debtId: hive.debtId,
          customerId: hive.customerId,
          businessOwnerId: hive.businessOwnerId,
          amount: hive.amount,
          paymentDate: hive.paymentDate,
          status: PaymentStatus.fromString(hive.status),
          notes: hive.notes,
          createdAt: hive.createdAt,
          updatedAt: hive.updatedAt,
        )).toList();
      }
    } catch (e) {
      // Fallback to local storage
      final hivePayments = await LocalStorageService.getPaymentsByBusinessOwner(businessOwnerId);
      return hivePayments.map((hive) => Payment(
        id: hive.id,
        debtId: hive.debtId,
        customerId: hive.customerId,
        businessOwnerId: hive.businessOwnerId,
        amount: hive.amount,
        paymentDate: hive.paymentDate,
        status: PaymentStatus.fromString(hive.status),
        notes: hive.notes,
        createdAt: hive.createdAt,
        updatedAt: hive.updatedAt,
      )).toList();
    }
  }

  /// Create payment from Payment object (for backup restore)
  static Future<Payment> createPaymentFromObject(Payment payment) async {
    try {
      if (_connectivity.isOnline) {
        // Insert to Supabase
        final response = await _client.payments
            .insert(payment.toInsertJson())
            .select()
            .single();

        final createdPayment = Payment.fromJson(response);
        await LocalStorageService.savePayment(createdPayment);
        return createdPayment;
      } else {
        // Save to local storage only (will sync later)
        await LocalStorageService.savePayment(payment, isSynced: false);
        return payment;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Get payment statistics for a business owner
  static Future<Map<String, dynamic>> getPaymentStatistics(String businessOwnerId) async {
    try {
      final payments = await getPaymentsByBusinessOwner(businessOwnerId);

      final totalPayments = payments.length;
      final completedPayments = payments.where((p) => p.isCompleted).length;
      final pendingPayments = payments.where((p) => p.isPending).length;
      final cancelledPayments = payments.where((p) => p.isCancelled).length;
      final totalAmount = payments.fold<double>(0, (sum, p) => sum + p.amount);
      final averagePaymentAmount = totalPayments > 0 ? totalAmount / totalPayments : 0.0;

      return {
        'totalPayments': totalPayments,
        'completedPayments': completedPayments,
        'pendingPayments': pendingPayments,
        'cancelledPayments': cancelledPayments,
        'totalAmount': totalAmount,
        'averagePaymentAmount': averagePaymentAmount,
      };
    } catch (e) {
      return {
        'totalPayments': 0,
        'completedPayments': 0,
        'pendingPayments': 0,
        'cancelledPayments': 0,
        'totalAmount': 0.0,
        'averagePaymentAmount': 0.0,
      };
    }
  }

  /// Get recent payments (last 30 days)
  static Future<List<Payment>> getRecentPayments(String customerId, {int days = 30}) async {
    try {
      final payments = await getPaymentsByCustomer(customerId);
      final cutoffDate = DateTime.now().subtract(Duration(days: days));
      
      return payments.where((payment) => payment.paymentDate.isAfter(cutoffDate)).toList();
    } catch (e) {
      return [];
    }
  }

  /// Sync unsynced payments when online
  static Future<void> syncUnsyncedPayments() async {
    if (!_connectivity.isOnline) return;

    try {
      final unsyncedPayments = await LocalStorageService.getUnsyncedPayments();
      
      for (final hivePayment in unsyncedPayments) {
        try {
          final payment = Payment(
            id: hivePayment.id,
            debtId: hivePayment.debtId,
            customerId: hivePayment.customerId,
            businessOwnerId: hivePayment.businessOwnerId,
            amount: hivePayment.amount,
            paymentDate: hivePayment.paymentDate,
            status: PaymentStatus.fromString(hivePayment.status),
            notes: hivePayment.notes,
            createdAt: hivePayment.createdAt,
            updatedAt: hivePayment.updatedAt,
          );

          // Try to sync to Supabase
          await _client.payments.upsert(payment.toJson());
          
          // Mark as synced
          await LocalStorageService.markPaymentAsSynced(payment.id);
        } catch (e) {
          // Continue with next payment if one fails
          continue;
        }
      }
    } catch (e) {
      // Sync failed, will retry later
    }
  }
}
