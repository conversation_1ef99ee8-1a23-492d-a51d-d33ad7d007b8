import 'package:hive/hive.dart';

part 'saved_login.g.dart';

@HiveType(typeId: 6)
class SavedLogin extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String storeName;

  @HiveField(2)
  String username;

  @HiveField(3)
  String password;

  @HiveField(4)
  DateTime createdAt;

  @HiveField(5)
  DateTime lastUsed;

  @HiveField(6)
  String? storeDescription;

  @HiveField(7)
  bool isFavorite;

  SavedLogin({
    required this.id,
    required this.storeName,
    required this.username,
    required this.password,
    required this.createdAt,
    required this.lastUsed,
    this.storeDescription,
    this.isFavorite = false,
  });

  // Create a new saved login
  factory SavedLogin.create({
    required String storeName,
    required String username,
    required String password,
    String? storeDescription,
  }) {
    final now = DateTime.now();
    return SavedLogin(
      id: '${now.millisecondsSinceEpoch}_${storeName.hashCode}',
      storeName: storeName,
      username: username,
      password: password,
      createdAt: now,
      lastUsed: now,
      storeDescription: storeDescription,
      isFavorite: false,
    );
  }

  // Update last used time
  void updateLastUsed() {
    lastUsed = DateTime.now();
    save();
  }

  // Toggle favorite status
  void toggleFavorite() {
    isFavorite = !isFavorite;
    save();
  }

  // Update login details
  void updateDetails({
    String? storeName,
    String? username,
    String? password,
    String? storeDescription,
  }) {
    if (storeName != null) this.storeName = storeName;
    if (username != null) this.username = username;
    if (password != null) this.password = password;
    if (storeDescription != null) this.storeDescription = storeDescription;
    save();
  }

  // Convert to map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'storeName': storeName,
      'username': username,
      'password': password,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
      'storeDescription': storeDescription,
      'isFavorite': isFavorite,
    };
  }

  // Create from map
  factory SavedLogin.fromMap(Map<String, dynamic> map) {
    return SavedLogin(
      id: map['id'],
      storeName: map['storeName'],
      username: map['username'],
      password: map['password'],
      createdAt: DateTime.parse(map['createdAt']),
      lastUsed: DateTime.parse(map['lastUsed']),
      storeDescription: map['storeDescription'],
      isFavorite: map['isFavorite'] ?? false,
    );
  }

  @override
  String toString() {
    return 'SavedLogin(id: $id, storeName: $storeName, username: $username)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SavedLogin && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
