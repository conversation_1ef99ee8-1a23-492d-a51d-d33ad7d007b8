import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/supabase_config.dart';

/// Safe Authentication service that handles Supabase availability
class SafeAuthService {
  /// Check if Supabase is available
  static bool get isSupabaseAvailable {
    try {
      final client = SupabaseConfig.client;
      return client.auth.currentUser != null || true; // Basic check
    } catch (e) {
      return false;
    }
  }

  /// Get current authenticated user
  static User? get currentUser {
    try {
      return SupabaseConfig.client.auth.currentUser;
    } catch (e) {
      return null;
    }
  }

  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  /// Get current user ID
  static String? get currentUserId => currentUser?.id;

  /// Business Owner Login
  static Future<AuthResponse> loginBusinessOwner({
    required String email,
    required String password,
  }) async {
    if (!isSupabaseAvailable) {
      throw Exception('Supabase not available');
    }
    
    try {
      final response = await SupabaseConfig.client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      return response;
    } catch (e) {
      throw Exception('خطأ في تسجيل الدخول: ${e.toString()}');
    }
  }

  /// Customer Login
  static Future<AuthResponse> loginCustomer({
    required String username,
    required String password,
  }) async {
    if (!isSupabaseAvailable) {
      throw Exception('Supabase not available');
    }
    
    try {
      // Create email from username for customer login
      final email = '$<EMAIL>';
      
      final response = await SupabaseConfig.client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      return response;
    } catch (e) {
      throw Exception('خطأ في تسجيل الدخول: ${e.toString()}');
    }
  }

  /// Register Business Owner
  static Future<AuthResponse> registerBusinessOwner({
    required String email,
    required String password,
    required String businessName,
    required String ownerName,
    String? phone,
    String? address,
  }) async {
    if (!isSupabaseAvailable) {
      throw Exception('Supabase not available');
    }
    
    try {
      final response = await SupabaseConfig.client.auth.signUp(
        email: email,
        password: password,
      );

      return response;
    } catch (e) {
      throw Exception('خطأ في التسجيل: ${e.toString()}');
    }
  }

  /// Reset Password
  static Future<void> resetBusinessOwnerPassword(String email) async {
    if (!isSupabaseAvailable) {
      throw Exception('Supabase not available');
    }
    
    try {
      await SupabaseConfig.client.auth.resetPasswordForEmail(email);
    } catch (e) {
      throw Exception('خطأ في إعادة تعيين كلمة المرور: ${e.toString()}');
    }
  }

  /// Sign Out
  static Future<void> signOut() async {
    if (!isSupabaseAvailable) {
      return; // Silently return if Supabase not available
    }
    
    try {
      await SupabaseConfig.client.auth.signOut();
    } catch (e) {
      // Ignore sign out errors
    }
  }

  /// Get auth state changes stream
  static Stream<AuthState> get authStateChanges {
    if (!isSupabaseAvailable) {
      return const Stream.empty();
    }
    
    try {
      return SupabaseConfig.client.auth.onAuthStateChange;
    } catch (e) {
      return const Stream.empty();
    }
  }

  /// Get current user profile (placeholder)
  static Future<dynamic> getCurrentUserProfile() async {
    if (!isSupabaseAvailable) {
      return null;
    }
    
    // This would normally fetch from database
    return null;
  }
}
