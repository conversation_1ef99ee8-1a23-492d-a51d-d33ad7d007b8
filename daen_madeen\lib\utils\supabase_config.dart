import 'package:supabase_flutter/supabase_flutter.dart';

/// Supabase configuration for دائن مدين (Creditor-Debtor) system
class SupabaseConfig {
  // TODO: Replace with your actual Supabase project credentials
  // Get these from: Supabase Dashboard > Settings > API
  static const String supabaseUrl = 'https://your-project-id.supabase.co';
  static const String supabaseAnonKey = 'your-anon-key-here';
  
  /// Initialize Supabase client
  static Future<void> initialize() async {
    // Check if credentials are configured
    if (supabaseUrl.contains('your-project-id') || supabaseAnonKey.contains('your-anon-key')) {
      throw Exception('Supabase credentials not configured. Please update supabase_config.dart with your actual Supabase project credentials.');
    }

    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      debug: true, // Set to false in production
    );
  }
  
  /// Get the Supabase client instance
  static SupabaseClient get client => Supabase.instance.client;
  
  /// Get the current user
  static User? get currentUser => client.auth.currentUser;
  
  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  /// Check if Supabase is properly configured
  static bool get isConfigured =>
      !supabaseUrl.contains('your-project-id') &&
      !supabaseAnonKey.contains('your-anon-key');
  
  /// Database table names
  static const String businessOwnersTable = 'business_owners';
  static const String customersTable = 'customers';
  static const String employeesTable = 'employees';
  static const String debtsTable = 'debts';
  static const String paymentsTable = 'payments';
  static const String notificationsTable = 'notifications';
  
  /// Real-time channel names
  static const String debtsChannel = 'debts_channel';
  static const String paymentsChannel = 'payments_channel';
  static const String notificationsChannel = 'notifications_channel';
  static const String customersChannel = 'customers_channel';
}

/// Extension methods for Supabase client
extension SupabaseClientExtension on SupabaseClient {
  /// Get business owners table
  PostgrestQueryBuilder get businessOwners => 
      from(SupabaseConfig.businessOwnersTable);
  
  /// Get customers table
  PostgrestQueryBuilder get customers =>
      from(SupabaseConfig.customersTable);

  /// Get employees table
  PostgrestQueryBuilder get employees =>
      from(SupabaseConfig.employeesTable);

  /// Get debts table
  PostgrestQueryBuilder get debts =>
      from(SupabaseConfig.debtsTable);
  
  /// Get payments table
  PostgrestQueryBuilder get payments => 
      from(SupabaseConfig.paymentsTable);
  
  /// Get notifications table
  PostgrestQueryBuilder get notifications => 
      from(SupabaseConfig.notificationsTable);
}
