import 'package:uuid/uuid.dart';

/// Report types enum
enum ReportType {
  customer,
  employee,
  financial,
  inventory,
  summary,
}

/// Report format enum
enum ReportFormat {
  pdf,
  excel,
  csv,
}

/// Report period enum
enum ReportPeriod {
  today,
  thisWeek,
  thisMonth,
  thisYear,
  custom,
  all,
}

/// Base report model
class Report {
  final String id;
  final String title;
  final String description;
  final ReportType type;
  final ReportFormat format;
  final ReportPeriod period;
  final DateTime? startDate;
  final DateTime? endDate;
  final String businessOwnerId;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final String? filePath;

  Report({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.format,
    required this.period,
    this.startDate,
    this.endDate,
    required this.businessOwnerId,
    required this.data,
    required this.createdAt,
    this.filePath,
  });

  /// Create a new report
  factory Report.create({
    required String title,
    required String description,
    required ReportType type,
    required ReportFormat format,
    required ReportPeriod period,
    DateTime? startDate,
    DateTime? endDate,
    required String businessOwnerId,
    required Map<String, dynamic> data,
    String? filePath,
  }) {
    return Report(
      id: const Uuid().v4(),
      title: title,
      description: description,
      type: type,
      format: format,
      period: period,
      startDate: startDate,
      endDate: endDate,
      businessOwnerId: businessOwnerId,
      data: data,
      createdAt: DateTime.now(),
      filePath: filePath,
    );
  }

  /// Copy with updated fields
  Report copyWith({
    String? id,
    String? title,
    String? description,
    ReportType? type,
    ReportFormat? format,
    ReportPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    String? businessOwnerId,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    String? filePath,
  }) {
    return Report(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      format: format ?? this.format,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      businessOwnerId: businessOwnerId ?? this.businessOwnerId,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      filePath: filePath ?? this.filePath,
    );
  }

  /// Get report type display name
  String get typeDisplayName {
    switch (type) {
      case ReportType.customer:
        return 'تقرير العملاء';
      case ReportType.employee:
        return 'تقرير الموظفين';
      case ReportType.financial:
        return 'التقرير المالي';
      case ReportType.inventory:
        return 'تقرير المخزون';
      case ReportType.summary:
        return 'التقرير الإجمالي';
    }
  }

  /// Get report format display name
  String get formatDisplayName {
    switch (format) {
      case ReportFormat.pdf:
        return 'PDF';
      case ReportFormat.excel:
        return 'Excel';
      case ReportFormat.csv:
        return 'CSV';
    }
  }

  /// Get report period display name
  String get periodDisplayName {
    switch (period) {
      case ReportPeriod.today:
        return 'اليوم';
      case ReportPeriod.thisWeek:
        return 'هذا الأسبوع';
      case ReportPeriod.thisMonth:
        return 'هذا الشهر';
      case ReportPeriod.thisYear:
        return 'هذا العام';
      case ReportPeriod.custom:
        return 'فترة مخصصة';
      case ReportPeriod.all:
        return 'جميع الفترات';
    }
  }

  /// Get formatted date range
  String get formattedDateRange {
    if (startDate == null || endDate == null) {
      return periodDisplayName;
    }
    
    final start = '${startDate!.day}/${startDate!.month}/${startDate!.year}';
    final end = '${endDate!.day}/${endDate!.month}/${endDate!.year}';
    return 'من $start إلى $end';
  }

  @override
  String toString() {
    return 'Report(id: $id, title: $title, type: $type)';
  }
}

/// Customer report data model
class CustomerReportData {
  final String customerId;
  final String customerName;
  final String? phone;
  final double totalDebts;
  final double totalPayments;
  final double remainingBalance;
  final List<DebtReportItem> debts;
  final List<PaymentReportItem> payments;
  final DateTime? lastTransactionDate;

  CustomerReportData({
    required this.customerId,
    required this.customerName,
    this.phone,
    required this.totalDebts,
    required this.totalPayments,
    required this.remainingBalance,
    required this.debts,
    required this.payments,
    this.lastTransactionDate,
  });
}

/// Employee report data model
class EmployeeReportData {
  final String employeeId;
  final String employeeName;
  final String position;
  final double salary;
  final DateTime hireDate;
  final String? phone;
  final String? email;
  final bool isActive;
  final int workingDays;
  final double totalSalaryPaid;
  final String employmentDuration;

  EmployeeReportData({
    required this.employeeId,
    required this.employeeName,
    required this.position,
    required this.salary,
    required this.hireDate,
    this.phone,
    this.email,
    required this.isActive,
    required this.workingDays,
    required this.totalSalaryPaid,
    required this.employmentDuration,
  });
}

/// Debt report item
class DebtReportItem {
  final String id;
  final String description;
  final double amount;
  final DateTime date;
  final String? notes;

  DebtReportItem({
    required this.id,
    required this.description,
    required this.amount,
    required this.date,
    this.notes,
  });
}

/// Payment report item
class PaymentReportItem {
  final String id;
  final double amount;
  final DateTime date;
  final String? notes;
  final String? method;

  PaymentReportItem({
    required this.id,
    required this.amount,
    required this.date,
    this.notes,
    this.method,
  });
}

/// Financial summary data
class FinancialSummaryData {
  final double totalRevenue;
  final double totalExpenses;
  final double netProfit;
  final double totalDebts;
  final double totalPayments;
  final double outstandingBalance;
  final int totalCustomers;
  final int activeCustomers;
  final int totalEmployees;
  final int activeEmployees;
  final double totalSalaries;

  FinancialSummaryData({
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netProfit,
    required this.totalDebts,
    required this.totalPayments,
    required this.outstandingBalance,
    required this.totalCustomers,
    required this.activeCustomers,
    required this.totalEmployees,
    required this.activeEmployees,
    required this.totalSalaries,
  });
}
