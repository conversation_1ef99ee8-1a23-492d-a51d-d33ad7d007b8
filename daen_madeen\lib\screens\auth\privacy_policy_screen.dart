import 'package:flutter/material.dart';

/// Privacy Policy Screen for دائن مدين (Creditor-Debtor) system
class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late ScrollController _scrollController;
  bool _hasScrolledToBottom = false;
  bool _hasAgreed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));
    
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 50) {
      if (!_hasScrolledToBottom) {
        setState(() {
          _hasScrolledToBottom = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              FadeTransition(
                opacity: _fadeAnimation,
                child: Container(
                  padding: const EdgeInsets.all(24),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const Expanded(
                        child: Text(
                          'سياسة الخصوصية',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 48), // Balance the back button
                    ],
                  ),
                ),
              ),

              // Content
              Expanded(
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 24),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Privacy Policy Content
                        Expanded(
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // App Icon and Title
                                Center(
                                  child: Column(
                                    children: [
                                      Container(
                                        width: 80,
                                        height: 80,
                                        decoration: BoxDecoration(
                                          color: const Color(0xFF3B82F6).withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(20),
                                        ),
                                        child: const Icon(
                                          Icons.security,
                                          size: 40,
                                          color: Color(0xFF3B82F6),
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      const Text(
                                        'سياسة الخصوصية لتطبيق دائن مدين',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF1F2937),
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'آخر تحديث: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFF6B7280),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                
                                const SizedBox(height: 30),
                                
                                _buildSection(
                                  '1. مقدمة',
                                  'نحن في تطبيق "دائن مدين" نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية. توضح هذه السياسة كيفية جمع واستخدام وحماية معلوماتك عند استخدام تطبيقنا.',
                                ),
                                
                                _buildSection(
                                  '2. البيانات التي نجمعها',
                                  '• معلومات الحساب: الاسم، البريد الإلكتروني، رقم الهاتف\n• البيانات المالية: معلومات الديون والمدفوعات\n• بيانات الاستخدام: كيفية تفاعلك مع التطبيق\n• معلومات الجهاز: نوع الجهاز ونظام التشغيل',
                                ),
                                
                                _buildSection(
                                  '3. كيفية استخدام البيانات',
                                  '• تقديم خدمات إدارة الديون والمدفوعات\n• تحسين تجربة المستخدم\n• إرسال إشعارات مهمة\n• ضمان أمان الحساب\n• الامتثال للمتطلبات القانونية',
                                ),
                                
                                _buildSection(
                                  '4. مشاركة البيانات',
                                  'نحن لا نبيع أو نؤجر أو نشارك بياناتك الشخصية مع أطراف ثالثة إلا في الحالات التالية:\n• بموافقتك الصريحة\n• للامتثال للقوانين\n• لحماية حقوقنا وحقوق المستخدمين',
                                ),
                                
                                _buildSection(
                                  '5. أمان البيانات',
                                  'نستخدم تقنيات التشفير المتقدمة وإجراءات الأمان الصارمة لحماية بياناتك من الوصول غير المصرح به أو التسريب أو التلف.',
                                ),
                                
                                _buildSection(
                                  '6. حقوقك',
                                  '• الوصول إلى بياناتك الشخصية\n• تصحيح البيانات غير الصحيحة\n• حذف حسابك وبياناتك\n• نقل بياناتك\n• الاعتراض على معالجة بياناتك',
                                ),
                                
                                _buildSection(
                                  '7. الاتصال بنا',
                                  'إذا كان لديك أي أسئلة حول سياسة الخصوصية، يمكنك التواصل معنا عبر:\nالبريد الإلكتروني: <EMAIL>\nالهاتف: +967 777747150',
                                ),
                                
                                const SizedBox(height: 30),
                              ],
                            ),
                          ),
                        ),
                        
                        // Agreement Section
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF9FAFB),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: const Color(0xFFE5E7EB),
                            ),
                          ),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Checkbox(
                                    value: _hasAgreed,
                                    onChanged: _hasScrolledToBottom ? (value) {
                                      setState(() {
                                        _hasAgreed = value ?? false;
                                      });
                                    } : null,
                                    activeColor: const Color(0xFF10B981),
                                  ),
                                  const Expanded(
                                    child: Text(
                                      'لقد قرأت وفهمت وأوافق على سياسة الخصوصية',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF374151),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              
                              if (!_hasScrolledToBottom)
                                const Padding(
                                  padding: EdgeInsets.only(top: 8),
                                  child: Text(
                                    'يرجى قراءة سياسة الخصوصية كاملة للمتابعة',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Color(0xFFEF4444),
                                    ),
                                  ),
                                ),
                              
                              const SizedBox(height: 16),
                              
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: _hasAgreed ? () {
                                        Navigator.pop(context, true);
                                      } : null,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: const Color(0xFF10B981),
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(vertical: 16),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        elevation: 2,
                                      ),
                                      child: const Text(
                                        'موافق ومتابعة',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                  
                                  const SizedBox(width: 12),
                                  
                                  Expanded(
                                    child: OutlinedButton(
                                      onPressed: () {
                                        Navigator.pop(context, false);
                                      },
                                      style: OutlinedButton.styleFrom(
                                        foregroundColor: const Color(0xFF6B7280),
                                        padding: const EdgeInsets.symmetric(vertical: 16),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        side: const BorderSide(
                                          color: Color(0xFFD1D5DB),
                                        ),
                                      ),
                                      child: const Text(
                                        'رفض',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF4B5563),
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
