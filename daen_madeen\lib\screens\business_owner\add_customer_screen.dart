import 'package:flutter/material.dart';
import '../../models/models.dart';

/// Add/Edit customer screen for business owners
class AddCustomerScreen extends StatefulWidget {
  final Customer? customer; // null for add, non-null for edit

  const AddCustomerScreen({super.key, this.customer});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _initialBalanceController = TextEditingController();
  final _maxDebtController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isActive = true;
  bool _isLoading = false;

  bool get isEditing => widget.customer != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _nameController.text = widget.customer!.name;
      _emailController.text = widget.customer!.username; // Using username as email for now
      _phoneController.text = widget.customer!.phone ?? '';
      _maxDebtController.text = widget.customer!.creditLimit.toString();
      _isActive = widget.customer!.isActive;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _initialBalanceController.dispose();
    _maxDebtController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'تعديل العميل' : 'إضافة عميل جديد'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Name field
              TextFormField(
                controller: _nameController,
                textDirection: TextDirection.rtl,
                decoration: const InputDecoration(
                  labelText: 'اسم العميل',
                  hintText: 'أدخل اسم العميل',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.person),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال اسم العميل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Email field
              TextFormField(
                controller: _emailController,
                textDirection: TextDirection.rtl,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'اسم الإيميل',
                  hintText: 'أدخل الإيميل',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.email),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Phone field
              TextFormField(
                controller: _phoneController,
                textDirection: TextDirection.rtl,
                keyboardType: TextInputType.phone,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  hintText: 'أدخل رقم الهاتف',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.phone),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال رقم الهاتف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Address field
              TextFormField(
                controller: _addressController,
                textDirection: TextDirection.rtl,
                decoration: const InputDecoration(
                  labelText: 'العنوان',
                  hintText: 'أدخل العنوان',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.location_on),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Initial balance field
              TextFormField(
                controller: _initialBalanceController,
                textDirection: TextDirection.rtl,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'الرصيد الافتتاحي "بالريال يمني"',
                  hintText: '0',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.account_balance_wallet),
                  suffixText: '0',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Max debt field
              TextFormField(
                controller: _maxDebtController,
                textDirection: TextDirection.rtl,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'حد أقصى للمديونية "بالريال يمني"',
                  hintText: '0',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.credit_card),
                  suffixText: '0',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Notes field
              TextFormField(
                controller: _notesController,
                textDirection: TextDirection.rtl,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  hintText: 'أدخل ملاحظات إضافية',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.note),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Active switch
              SwitchListTile(
                title: const Text('العميل نشط'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
              ),
              const SizedBox(height: 32),

              // Save button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _saveCustomer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF27AE60),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: Text(
                    isEditing ? 'حفظ التعديلات' : 'إضافة العميل',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveCustomer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final name = _nameController.text.trim();
      final email = _emailController.text.trim();
      final phone = _phoneController.text.trim();
      final address = _addressController.text.trim();
      final initialBalanceText = _initialBalanceController.text.trim();
      final maxDebtText = _maxDebtController.text.trim();
      final notes = _notesController.text.trim();

      final initialBalance = initialBalanceText.isEmpty ? 0.0 : double.parse(initialBalanceText);
      final maxDebt = maxDebtText.isEmpty ? 0.0 : double.parse(maxDebtText);

      if (isEditing) {
        // Update existing customer
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث بيانات العميل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Create new customer
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة العميل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }

      Navigator.pop(context, true);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حفظ البيانات: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
