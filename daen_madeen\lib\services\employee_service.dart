import '../models/models.dart';
import '../utils/supabase_config.dart';
import 'connectivity_service.dart';
import 'local_storage_service.dart';

/// Employee service for دائن مدين (Creditor-Debtor) system
class EmployeeService {
  static final _client = SupabaseConfig.client;
  static final _connectivity = ConnectivityService();

  /// Get all employees for a business owner
  static Future<List<Employee>> getEmployeesByBusinessOwner(String businessOwnerId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.employees
            .select()
            .eq('business_owner_id', businessOwnerId)
            .order('created_at', ascending: false);

        final employees = response.map((json) => Employee.fromJson(json)).toList();

        // Save to local storage
        for (final employee in employees) {
          await LocalStorageService.saveEmployee(employee);
        }

        return employees;
      } else {
        // Fetch from local storage
        final hiveEmployees = await LocalStorageService.getEmployeesByBusinessOwner(businessOwnerId);
        return hiveEmployees.map((hive) => Employee(
          id: hive.id,
          businessOwnerId: hive.businessOwnerId,
          authUserId: hive.authUserId,
          name: hive.name,
          phone: hive.phone,
          email: hive.email,
          position: hive.position,
          salary: hive.salary,
          address: hive.address,
          nationalId: hive.nationalId,
          hireDate: hive.hireDate,
          isActive: hive.isActive,
          notes: hive.notes,
          createdAt: hive.createdAt,
          updatedAt: hive.updatedAt,
        )).toList();
      }
    } catch (e) {
      // Fallback to local storage on error
      final hiveEmployees = await LocalStorageService.getEmployeesByBusinessOwner(businessOwnerId);
      return hiveEmployees.map((hive) => Employee(
        id: hive.id,
        businessOwnerId: hive.businessOwnerId,
        authUserId: hive.authUserId,
        name: hive.name,
        phone: hive.phone,
        email: hive.email,
        position: hive.position,
        salary: hive.salary,
        address: hive.address,
        nationalId: hive.nationalId,
        hireDate: hive.hireDate,
        isActive: hive.isActive,
        notes: hive.notes,
        createdAt: hive.createdAt,
        updatedAt: hive.updatedAt,
      )).toList();
    }
  }

  /// Get employee by ID
  static Future<Employee?> getEmployeeById(String employeeId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.employees
            .select()
            .eq('id', employeeId)
            .maybeSingle();

        if (response != null) {
          final employee = Employee.fromJson(response);
          await LocalStorageService.saveEmployee(employee);
          return employee;
        }
      } else {
        // Fetch from local storage
        final hiveEmployee = await LocalStorageService.getEmployee(employeeId);
        if (hiveEmployee != null) {
          return Employee(
            id: hiveEmployee.id,
            businessOwnerId: hiveEmployee.businessOwnerId,
            authUserId: hiveEmployee.authUserId,
            name: hiveEmployee.name,
            phone: hiveEmployee.phone,
            email: hiveEmployee.email,
            position: hiveEmployee.position,
            salary: hiveEmployee.salary,
            address: hiveEmployee.address,
            nationalId: hiveEmployee.nationalId,
            hireDate: hiveEmployee.hireDate,
            isActive: hiveEmployee.isActive,
            notes: hiveEmployee.notes,
            createdAt: hiveEmployee.createdAt,
            updatedAt: hiveEmployee.updatedAt,
          );
        }
      }
      return null;
    } catch (e) {
      // Fallback to local storage
      final hiveEmployee = await LocalStorageService.getEmployee(employeeId);
      if (hiveEmployee != null) {
        return Employee(
          id: hiveEmployee.id,
          businessOwnerId: hiveEmployee.businessOwnerId,
          authUserId: hiveEmployee.authUserId,
          name: hiveEmployee.name,
          phone: hiveEmployee.phone,
          email: hiveEmployee.email,
          position: hiveEmployee.position,
          salary: hiveEmployee.salary,
          address: hiveEmployee.address,
          nationalId: hiveEmployee.nationalId,
          hireDate: hiveEmployee.hireDate,
          isActive: hiveEmployee.isActive,
          notes: hiveEmployee.notes,
          createdAt: hiveEmployee.createdAt,
          updatedAt: hiveEmployee.updatedAt,
        );
      }
      return null;
    }
  }

  /// Create a new employee
  static Future<Employee?> createEmployee({
    required String businessOwnerId,
    required String name,
    String? phone,
    String? email,
    required String position,
    required double salary,
    String? address,
    String? nationalId,
    DateTime? hireDate,
    String? notes,
  }) async {
    try {
      final employee = Employee.create(
        businessOwnerId: businessOwnerId,
        authUserId: '', // Will be set when auth account is created
        name: name,
        phone: phone,
        email: email,
        position: position,
        salary: salary,
        address: address,
        nationalId: nationalId,
        hireDate: hireDate,
        notes: notes,
      );

      if (_connectivity.isOnline) {
        // Insert to Supabase
        final response = await _client.employees
            .insert(employee.toInsertJson())
            .select()
            .single();

        final createdEmployee = Employee.fromJson(response);
        await LocalStorageService.saveEmployee(createdEmployee);
        return createdEmployee;
      } else {
        // Save to local storage only (will sync later)
        await LocalStorageService.saveEmployee(employee);
        return employee;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Update employee
  static Future<Employee?> updateEmployee(Employee employee) async {
    try {
      if (_connectivity.isOnline) {
        // Update in Supabase
        final response = await _client.employees
            .update(employee.toJson())
            .eq('id', employee.id)
            .select()
            .single();

        final updatedEmployee = Employee.fromJson(response);
        await LocalStorageService.saveEmployee(updatedEmployee);
        return updatedEmployee;
      } else {
        // Update in local storage only (will sync later)
        await LocalStorageService.saveEmployee(employee);
        return employee;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Delete employee
  static Future<bool> deleteEmployee(String employeeId) async {
    try {
      if (_connectivity.isOnline) {
        // Delete from Supabase
        await _client.employees.delete().eq('id', employeeId);
      }
      
      // Delete from local storage
      await LocalStorageService.deleteEmployee(employeeId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get active employees count
  static Future<int> getActiveEmployeesCount(String businessOwnerId) async {
    try {
      final employees = await getEmployeesByBusinessOwner(businessOwnerId);
      return employees.where((employee) => employee.isActive).length;
    } catch (e) {
      return 0;
    }
  }

  /// Get total salary expenses
  static Future<double> getTotalSalaryExpenses(String businessOwnerId) async {
    try {
      final employees = await getEmployeesByBusinessOwner(businessOwnerId);
      double total = 0.0;
      for (final employee in employees) {
        if (employee.isActive) {
          total += employee.salary;
        }
      }
      return total;
    } catch (e) {
      return 0.0;
    }
  }

  /// Search employees by name or position
  static Future<List<Employee>> searchEmployees(String businessOwnerId, String query) async {
    try {
      final employees = await getEmployeesByBusinessOwner(businessOwnerId);
      return employees.where((employee) {
        return employee.name.toLowerCase().contains(query.toLowerCase()) ||
               employee.position.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Check if national ID already exists
  static Future<bool> _checkNationalIdExists(String nationalId, String businessOwnerId, [String? excludeEmployeeId]) async {
    try {
      final employees = await getEmployeesByBusinessOwner(businessOwnerId);
      return employees.any((employee) => 
          employee.nationalId == nationalId && 
          employee.id != excludeEmployeeId);
    } catch (e) {
      return false;
    }
  }

  /// Validate employee data
  static Future<String?> validateEmployeeData({
    required String name,
    required String position,
    required double salary,
    String? nationalId,
    required String businessOwnerId,
    String? excludeEmployeeId,
  }) async {
    if (name.trim().isEmpty) {
      return 'يرجى إدخال اسم الموظف';
    }
    
    if (position.trim().isEmpty) {
      return 'يرجى إدخال منصب الموظف';
    }
    
    if (salary <= 0) {
      return 'يرجى إدخال راتب صحيح';
    }
    
    if (nationalId != null && nationalId.isNotEmpty) {
      final exists = await _checkNationalIdExists(nationalId, businessOwnerId, excludeEmployeeId);
      if (exists) {
        return 'رقم الهوية مستخدم بالفعل';
      }
    }
    
    return null;
  }

  /// Create username and password for employee
  static Future<bool> createEmployeeUsername({
    required String employeeId,
    required String username,
    required String password,
  }) async {
    try {
      // Check if username already exists
      final existingEmployee = await _checkEmployeeUsernameExists(username);
      if (existingEmployee) {
        throw Exception('اسم المستخدم موجود بالفعل');
      }

      // Get the employee
      final employee = await getEmployeeById(employeeId);
      if (employee == null) {
        throw Exception('الموظف غير موجود');
      }

      // In demo mode, just update the employee with a dummy auth user ID
      final updatedEmployee = employee.copyWith(
        authUserId: 'emp_${DateTime.now().millisecondsSinceEpoch}',
        updatedAt: DateTime.now(),
      );

      await updateEmployee(updatedEmployee);
      return true;
    } catch (e) {
      rethrow;
    }
  }

  /// Check if employee username already exists
  static Future<bool> _checkEmployeeUsernameExists(String username) async {
    try {
      // This would normally check against the auth system
      // For now, we'll just return false to allow creation
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Get employees without usernames
  static Future<List<Employee>> getEmployeesWithoutUsernames(String businessOwnerId) async {
    try {
      final employees = await getEmployeesByBusinessOwner(businessOwnerId);
      return employees.where((employee) =>
          employee.authUserId.isEmpty || employee.authUserId == '').toList();
    } catch (e) {
      return [];
    }
  }

  /// Create usernames for multiple employees
  static Future<Map<String, bool>> createMultipleEmployeeUsernames({
    required Map<String, Map<String, String>> employeeCredentials,
  }) async {
    final results = <String, bool>{};

    for (final entry in employeeCredentials.entries) {
      final employeeId = entry.key;
      final credentials = entry.value;
      final username = credentials['username'] ?? '';
      final password = credentials['password'] ?? '';

      try {
        final success = await createEmployeeUsername(
          employeeId: employeeId,
          username: username,
          password: password,
        );
        results[employeeId] = success;
      } catch (e) {
        results[employeeId] = false;
      }
    }

    return results;
  }
}
