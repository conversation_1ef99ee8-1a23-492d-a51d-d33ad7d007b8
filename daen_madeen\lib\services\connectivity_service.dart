import 'dart:async';
import 'package:flutter/foundation.dart';

/// Connectivity service for دائن مدين (<PERSON><PERSON>-Debtor) system
/// Manages online/offline status and provides connectivity information
class ConnectivityService extends ChangeNotifier {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  bool _isOnline = true;
  StreamController<bool>? _connectivityController;

  /// Get current online status
  bool get isOnline => _isOnline;

  /// Get offline status
  bool get isOffline => !_isOnline;

  /// Stream of connectivity changes
  Stream<bool> get connectivityStream {
    _connectivityController ??= StreamController<bool>.broadcast();
    return _connectivityController!.stream;
  }

  /// Initialize connectivity service
  Future<void> initialize() async {
    // For now, we'll assume we're online
    // In a real implementation, you would use connectivity_plus package
    _isOnline = true;
    
    // Simulate connectivity changes for testing
    _startConnectivityMonitoring();
  }

  /// Start monitoring connectivity
  void _startConnectivityMonitoring() {
    // This is a simplified implementation
    // In production, use connectivity_plus package
    Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkConnectivity();
    });
  }

  /// Check connectivity status
  Future<void> _checkConnectivity() async {
    try {
      // Simple connectivity check
      // In production, implement proper connectivity checking
      final wasOnline = _isOnline;
      
      // For now, assume we're always online
      // You can implement actual connectivity checking here
      _isOnline = true;
      
      if (wasOnline != _isOnline) {
        _notifyConnectivityChange();
      }
    } catch (e) {
      if (_isOnline) {
        _isOnline = false;
        _notifyConnectivityChange();
      }
    }
  }

  /// Manually set online status (for testing)
  void setOnlineStatus(bool isOnline) {
    if (_isOnline != isOnline) {
      _isOnline = isOnline;
      _notifyConnectivityChange();
    }
  }

  /// Notify listeners about connectivity changes
  void _notifyConnectivityChange() {
    notifyListeners();
    _connectivityController?.add(_isOnline);
    
    if (kDebugMode) {
      print('Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
    }
  }

  /// Force connectivity check
  Future<bool> checkConnectivity() async {
    await _checkConnectivity();
    return _isOnline;
  }

  /// Dispose resources
  @override
  void dispose() {
    _connectivityController?.close();
    super.dispose();
  }
}

/// Connectivity provider for easy access in widgets
class ConnectivityProvider extends ChangeNotifier {
  final ConnectivityService _connectivityService = ConnectivityService();
  StreamSubscription<bool>? _connectivitySubscription;

  ConnectivityProvider() {
    _initialize();
  }

  /// Get connectivity service instance
  ConnectivityService get connectivityService => _connectivityService;

  /// Check if device is online
  bool get isOnline => _connectivityService.isOnline;

  /// Check if device is offline
  bool get isOffline => _connectivityService.isOffline;

  /// Initialize provider
  void _initialize() {
    _connectivityService.initialize();
    
    // Listen to connectivity changes
    _connectivitySubscription = _connectivityService.connectivityStream.listen(
      (isOnline) {
        notifyListeners();
      },
    );
  }

  /// Force connectivity check
  Future<bool> checkConnectivity() async {
    final result = await _connectivityService.checkConnectivity();
    notifyListeners();
    return result;
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}
