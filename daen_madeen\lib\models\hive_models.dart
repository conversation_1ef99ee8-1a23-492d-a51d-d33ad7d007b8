import 'package:hive/hive.dart';

part 'hive_models.g.dart';

/// Hive model for Business Owner local storage
@HiveType(typeId: 0)
class HiveBusinessOwner extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String authUserId;

  @HiveField(2)
  String businessName;

  @HiveField(3)
  String ownerName;

  @HiveField(4)
  String email;

  @HiveField(5)
  String? phone;

  @HiveField(6)
  String? address;

  @HiveField(7)
  DateTime createdAt;

  @HiveField(8)
  DateTime updatedAt;

  HiveBusinessOwner({
    required this.id,
    required this.authUserId,
    required this.businessName,
    required this.ownerName,
    required this.email,
    this.phone,
    this.address,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Convert from BusinessOwner model
  factory HiveBusinessOwner.fromBusinessOwner(dynamic businessOwner) {
    return HiveBusinessOwner(
      id: businessOwner.id,
      authUserId: businessOwner.authUserId,
      businessName: businessOwner.businessName,
      ownerName: businessOwner.ownerName,
      email: businessOwner.email,
      phone: businessOwner.phone,
      address: businessOwner.address,
      createdAt: businessOwner.createdAt,
      updatedAt: businessOwner.updatedAt,
    );
  }
}

/// Hive model for Customer local storage
@HiveType(typeId: 1)
class HiveCustomer extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String businessOwnerId;

  @HiveField(2)
  String authUserId;

  @HiveField(3)
  String name;

  @HiveField(4)
  String? phone;

  @HiveField(5)
  String username;

  @HiveField(6)
  double creditLimit;

  @HiveField(7)
  double currentBalance;

  @HiveField(8)
  bool isActive;

  @HiveField(9)
  DateTime createdAt;

  @HiveField(10)
  DateTime updatedAt;

  HiveCustomer({
    required this.id,
    required this.businessOwnerId,
    required this.authUserId,
    required this.name,
    this.phone,
    required this.username,
    required this.creditLimit,
    required this.currentBalance,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Convert from Customer model
  factory HiveCustomer.fromCustomer(dynamic customer) {
    return HiveCustomer(
      id: customer.id,
      businessOwnerId: customer.businessOwnerId,
      authUserId: customer.authUserId,
      name: customer.name,
      phone: customer.phone,
      username: customer.username,
      creditLimit: customer.creditLimit,
      currentBalance: customer.currentBalance,
      isActive: customer.isActive,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    );
  }
}

/// Hive model for Debt local storage
@HiveType(typeId: 2)
class HiveDebt extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String customerId;

  @HiveField(2)
  String businessOwnerId;

  @HiveField(3)
  double amount;

  @HiveField(4)
  String? description;

  @HiveField(5)
  DateTime dateCreated;

  @HiveField(6)
  bool isPaid;

  @HiveField(7)
  DateTime createdAt;

  @HiveField(8)
  DateTime updatedAt;

  @HiveField(9)
  bool isSynced; // Track sync status

  HiveDebt({
    required this.id,
    required this.customerId,
    required this.businessOwnerId,
    required this.amount,
    this.description,
    required this.dateCreated,
    required this.isPaid,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  /// Convert from Debt model
  factory HiveDebt.fromDebt(dynamic debt, {bool isSynced = true}) {
    return HiveDebt(
      id: debt.id,
      customerId: debt.customerId,
      businessOwnerId: debt.businessOwnerId,
      amount: debt.amount,
      description: debt.description,
      dateCreated: debt.dateCreated,
      isPaid: debt.isPaid,
      createdAt: debt.createdAt,
      updatedAt: debt.updatedAt,
      isSynced: isSynced,
    );
  }
}

/// Hive model for Payment local storage
@HiveType(typeId: 3)
class HivePayment extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String debtId;

  @HiveField(2)
  String customerId;

  @HiveField(3)
  String businessOwnerId;

  @HiveField(4)
  double amount;

  @HiveField(5)
  DateTime paymentDate;

  @HiveField(6)
  String status; // Store as string

  @HiveField(7)
  String? notes;

  @HiveField(8)
  DateTime createdAt;

  @HiveField(9)
  DateTime updatedAt;

  @HiveField(10)
  bool isSynced; // Track sync status

  HivePayment({
    required this.id,
    required this.debtId,
    required this.customerId,
    required this.businessOwnerId,
    required this.amount,
    required this.paymentDate,
    required this.status,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  /// Convert from Payment model
  factory HivePayment.fromPayment(dynamic payment, {bool isSynced = true}) {
    return HivePayment(
      id: payment.id,
      debtId: payment.debtId,
      customerId: payment.customerId,
      businessOwnerId: payment.businessOwnerId,
      amount: payment.amount,
      paymentDate: payment.paymentDate,
      status: payment.status.toDbString(),
      notes: payment.notes,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
      isSynced: isSynced,
    );
  }
}

/// Hive model for Notification local storage
@HiveType(typeId: 4)
class HiveNotification extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String recipientId;

  @HiveField(2)
  String senderId;

  @HiveField(3)
  String recipientType; // Store as string

  @HiveField(4)
  String title;

  @HiveField(5)
  String message;

  @HiveField(6)
  bool isRead;

  @HiveField(7)
  String? relatedDebtId;

  @HiveField(8)
  String? relatedPaymentId;

  @HiveField(9)
  DateTime createdAt;

  @HiveField(10)
  bool isSynced;

  @HiveField(11)
  String? businessOwnerId; // Track sync status

  HiveNotification({
    required this.id,
    required this.recipientId,
    required this.senderId,
    required this.recipientType,
    required this.title,
    required this.message,
    required this.isRead,
    this.relatedDebtId,
    this.relatedPaymentId,
    required this.createdAt,
    this.isSynced = false,
    this.businessOwnerId,
  });

  /// Convert from AppNotification model
  factory HiveNotification.fromNotification(dynamic notification, {bool isSynced = true, String? businessOwnerId}) {
    return HiveNotification(
      id: notification.id,
      recipientId: notification.recipientId,
      senderId: notification.senderId,
      recipientType: notification.recipientType.toDbString(),
      title: notification.title,
      message: notification.message,
      isRead: notification.isRead,
      relatedDebtId: notification.relatedDebtId,
      relatedPaymentId: notification.relatedPaymentId,
      createdAt: notification.createdAt,
      isSynced: isSynced,
      businessOwnerId: businessOwnerId,
    );
  }
}

/// Hive model for Employee local storage
@HiveType(typeId: 5)
class HiveEmployee extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String businessOwnerId;

  @HiveField(2)
  String authUserId;

  @HiveField(3)
  String name;

  @HiveField(4)
  String? phone;

  @HiveField(5)
  String? email;

  @HiveField(6)
  String position;

  @HiveField(7)
  double salary;

  @HiveField(8)
  String? address;

  @HiveField(9)
  String? nationalId;

  @HiveField(10)
  DateTime hireDate;

  @HiveField(11)
  bool isActive;

  @HiveField(12)
  String? notes;

  @HiveField(13)
  DateTime createdAt;

  @HiveField(14)
  DateTime updatedAt;

  @HiveField(15)
  bool isSynced;

  HiveEmployee({
    required this.id,
    required this.businessOwnerId,
    required this.authUserId,
    required this.name,
    this.phone,
    this.email,
    required this.position,
    required this.salary,
    this.address,
    this.nationalId,
    required this.hireDate,
    required this.isActive,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  /// Convert from Employee model
  factory HiveEmployee.fromEmployee(dynamic employee, {bool isSynced = false}) {
    return HiveEmployee(
      id: employee.id,
      businessOwnerId: employee.businessOwnerId,
      authUserId: employee.authUserId,
      name: employee.name,
      phone: employee.phone,
      email: employee.email,
      position: employee.position,
      salary: employee.salary,
      address: employee.address,
      nationalId: employee.nationalId,
      hireDate: employee.hireDate,
      isActive: employee.isActive,
      notes: employee.notes,
      createdAt: employee.createdAt,
      updatedAt: employee.updatedAt,
      isSynced: isSynced,
    );
  }
}
