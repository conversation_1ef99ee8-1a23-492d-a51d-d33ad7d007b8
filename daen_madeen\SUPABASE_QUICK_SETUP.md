# إعداد Supabase السريع لتطبيق دائن مدين

## الحالة الحالية ❌
التطبيق يعمل في **وضع التجريب** لأن Supabase غير مُعدّ.

## خطوات الإعداد السريع (15 دقيقة)

### 1️⃣ إنشاء مشروع Supabase
1. اذهب إلى: https://supabase.com
2. انقر **"Sign Up"** أو **"Sign In"**
3. انقر **"New Project"**
4. املأ البيانات:
   - **Name**: `daen-madeen`
   - **Database Password**: اختر كلمة مرور قوية (احفظها!)
   - **Region**: اختر الأقرب لك
5. انقر **"Create new project"**
6. انتظر 2-3 دقائق حتى يكتمل الإعداد

### 2️⃣ الحصول على بيانات الاعتماد
1. في لوحة تحكم Supabase، اذهب إلى **Settings** > **API**
2. انسخ هذين القيمتين:
   ```
   Project URL: https://your-project-id.supabase.co
   Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

### 3️⃣ تحديث إعدادات التطبيق
افتح ملف `lib/utils/supabase_config.dart` وغيّر:

```dart
// من:
static const String supabaseUrl = 'https://your-project-id.supabase.co';
static const String supabaseAnonKey = 'your-anon-key-here';

// إلى (ضع قيمك الحقيقية):
static const String supabaseUrl = 'https://abcdefgh.supabase.co';
static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
```

### 4️⃣ إعداد قاعدة البيانات
1. في لوحة تحكم Supabase، اذهب إلى **SQL Editor**
2. انسخ محتوى ملف `supabase_schema.sql` والصقه
3. انقر **"Run"**
4. انسخ محتوى ملف `supabase_rls_policies.sql` والصقه
5. انقر **"Run"**

### 5️⃣ تفعيل المصادقة
1. اذهب إلى **Authentication** > **Settings**
2. في **General**:
   - **Site URL**: `http://localhost:3000`
3. في **Email**:
   - تأكد من تفعيل **Enable email confirmations**

### 6️⃣ تفعيل Real-time
1. اذهب إلى **Database** > **Replication**
2. فعّل Real-time للجداول التالية:
   - `debts`
   - `payments`
   - `notifications`
   - `customers`

## اختبار الاتصال

بعد الإعداد، شغّل التطبيق:
```bash
flutter run -d chrome
```

### علامات نجاح الاتصال:
- ✅ لا توجد رسالة "Supabase not initialized"
- ✅ يمكن تسجيل حساب جديد
- ✅ يمكن تسجيل الدخول

### بيانات تجريبية للاختبار:
بعد الإعداد، يمكنك إنشاء حساب تجريبي:
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `123456`
- **اسم المحل**: `محل الأمانة`
- **اسم المالك**: `أحمد محمد`

## استكشاف الأخطاء

### خطأ في الاتصال:
- تأكد من صحة Project URL و Anon Key
- تأكد من عدم وجود مسافات إضافية

### خطأ في قاعدة البيانات:
- تأكد من تشغيل ملفات SQL بالترتيب الصحيح
- تحقق من عدم وجود أخطاء في SQL Editor

### خطأ في المصادقة:
- تأكد من تفعيل Email authentication
- تحقق من إعدادات Site URL

## الدعم
- دليل Supabase الكامل: `SUPABASE_SETUP.md`
- وثائق Supabase: https://supabase.com/docs
- مجتمع Supabase: https://discord.supabase.com

---
**ملاحظة**: بعد إعداد Supabase، ستحصل على تطبيق كامل الوظائف مع:
- مصادقة حقيقية
- حفظ البيانات في السحابة
- تحديثات فورية
- نسخ احتياطية تلقائية
