import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';

/// Add employee screen for business owners
class AddEmployeeScreen extends StatefulWidget {
  final Employee? employee;

  const AddEmployeeScreen({super.key, this.employee});

  @override
  State<AddEmployeeScreen> createState() => _AddEmployeeScreenState();
}

class _AddEmployeeScreenState extends State<AddEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _positionController = TextEditingController();
  final _salaryController = TextEditingController();
  final _addressController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isActive = true;
  DateTime _hireDate = DateTime.now();

  bool get isEditing => widget.employee != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _nameController.text = widget.employee!.name;
      _phoneController.text = widget.employee!.phone ?? '';
      _emailController.text = widget.employee!.email ?? '';
      _positionController.text = widget.employee!.position;
      _salaryController.text = widget.employee!.salary.toString();
      _addressController.text = widget.employee!.address ?? '';
      _nationalIdController.text = widget.employee!.nationalId ?? '';
      _notesController.text = widget.employee!.notes ?? '';
      _isActive = widget.employee!.isActive;
      _hireDate = widget.employee!.hireDate;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _positionController.dispose();
    _salaryController.dispose();
    _addressController.dispose();
    _nationalIdController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'تعديل الموظف' : 'إضافة موظف جديد'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Name field
              TextFormField(
                controller: _nameController,
                textDirection: TextDirection.rtl,
                decoration: const InputDecoration(
                  labelText: 'اسم الموظف',
                  hintText: 'أدخل اسم الموظف',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.person),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال اسم الموظف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Position field
              TextFormField(
                controller: _positionController,
                textDirection: TextDirection.rtl,
                decoration: const InputDecoration(
                  labelText: 'المنصب',
                  hintText: 'أدخل منصب الموظف',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.work),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال منصب الموظف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Salary field
              TextFormField(
                controller: _salaryController,
                textDirection: TextDirection.rtl,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'الراتب (ريال)',
                  hintText: 'أدخل راتب الموظف',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.attach_money),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال راتب الموظف';
                  }
                  final salary = double.tryParse(value);
                  if (salary == null || salary <= 0) {
                    return 'يرجى إدخال راتب صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Phone field
              TextFormField(
                controller: _phoneController,
                textDirection: TextDirection.rtl,
                keyboardType: TextInputType.phone,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف (اختياري)',
                  hintText: 'أدخل رقم الهاتف',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.phone),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Email field
              TextFormField(
                controller: _emailController,
                textDirection: TextDirection.rtl,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني (اختياري)',
                  hintText: 'أدخل البريد الإلكتروني',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.email),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // National ID field
              TextFormField(
                controller: _nationalIdController,
                textDirection: TextDirection.rtl,
                decoration: const InputDecoration(
                  labelText: 'رقم الهوية (اختياري)',
                  hintText: 'أدخل رقم الهوية',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.badge),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Address field
              TextFormField(
                controller: _addressController,
                textDirection: TextDirection.rtl,
                decoration: const InputDecoration(
                  labelText: 'العنوان (اختياري)',
                  hintText: 'أدخل العنوان',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.location_on),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Hire date field
              InkWell(
                onTap: _selectHireDate,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ التوظيف',
                    prefixIcon: Icon(Icons.calendar_today),
                    border: OutlineInputBorder(),
                  ),
                  child: Text(
                    '${_hireDate.day}/${_hireDate.month}/${_hireDate.year}',
                    textDirection: TextDirection.rtl,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Notes field
              TextFormField(
                controller: _notesController,
                textDirection: TextDirection.rtl,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  hintText: 'أدخل ملاحظات إضافية',
                  hintTextDirection: TextDirection.rtl,
                  prefixIcon: Icon(Icons.note),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Active switch
              SwitchListTile(
                title: const Text('الموظف نشط'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
              ),
              const SizedBox(height: 32),

              // Save button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _saveEmployee,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF27AE60),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: Text(
                    isEditing ? 'حفظ التعديلات' : 'إضافة الموظف',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectHireDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _hireDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _hireDate) {
      setState(() {
        _hireDate = picked;
      });
    }
  }

  void _saveEmployee() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final userProfile = authProvider.userProfile;
      
      if (userProfile == null || userProfile is! BusinessOwner) {
        throw Exception('لم يتم العثور على بيانات صاحب العمل');
      }
      
      final businessOwner = userProfile as BusinessOwner;

      final name = _nameController.text.trim();
      final position = _positionController.text.trim();
      final salary = double.parse(_salaryController.text.trim());
      final phone = _phoneController.text.trim();
      final email = _emailController.text.trim();
      final address = _addressController.text.trim();
      final nationalId = _nationalIdController.text.trim();
      final notes = _notesController.text.trim();

      if (isEditing) {
        // Update existing employee
        final updatedEmployee = widget.employee!.copyWith(
          name: name,
          position: position,
          salary: salary,
          phone: phone.isEmpty ? null : phone,
          email: email.isEmpty ? null : email,
          address: address.isEmpty ? null : address,
          nationalId: nationalId.isEmpty ? null : nationalId,
          hireDate: _hireDate,
          isActive: _isActive,
          notes: notes.isEmpty ? null : notes,
          updatedAt: DateTime.now(),
        );

        await EmployeeService.updateEmployee(updatedEmployee);
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث بيانات الموظف بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Create new employee
        await EmployeeService.createEmployee(
          businessOwnerId: businessOwner.id,
          name: name,
          position: position,
          salary: salary,
          phone: phone.isEmpty ? null : phone,
          email: email.isEmpty ? null : email,
          address: address.isEmpty ? null : address,
          nationalId: nationalId.isEmpty ? null : nationalId,
          hireDate: _hireDate,
          notes: notes.isEmpty ? null : notes,
        );

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة الموظف بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }

      Navigator.pop(context, true);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حفظ البيانات: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
