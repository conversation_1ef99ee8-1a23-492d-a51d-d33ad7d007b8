import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';

/// Backup and restore screen for business owners
class BackupScreen extends StatefulWidget {
  const BackupScreen({super.key});

  @override
  State<BackupScreen> createState() => _BackupScreenState();
}

class _BackupScreenState extends State<BackupScreen> {
  bool _isExporting = false;
  bool _isImporting = false;
  bool _isPartialImporting = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تخزين واستعادة البيانات'),
        backgroundColor: const Color(0xFF0A0E27),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const SizedBox(height: 20),

                // Main backup card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Header with icon
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Icon(
                            Icons.arrow_forward,
                            color: Color(0xFF2D3561),
                            size: 24,
                          ),
                          Text(
                            'تخزين واستعادة البيانات',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                            textDirection: TextDirection.rtl,
                          ),
                        ],
                      ),

                      const SizedBox(height: 30),

                      // Export to Drive button
                      _buildBackupButton(
                        text: 'نسخ البيانات إلى درايف',
                        icon: Icons.cloud_upload,
                        onTap: () => _exportToFile(),
                        isLoading: _isExporting,
                      ),

                      const SizedBox(height: 16),

                      // Import from Drive button
                      _buildBackupButton(
                        text: 'استعادة البيانات من درايف',
                        icon: Icons.cloud_download,
                        onTap: () => _importFromFile(),
                        isLoading: _isImporting,
                      ),

                      const SizedBox(height: 16),

                      // Partial import button
                      _buildBackupButton(
                        text: 'استعادة لبيانات محدوده',
                        icon: Icons.folder_special,
                        onTap: () => _showPartialImportDialog(),
                        isLoading: _isPartialImporting,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 30),

                // Info section
                _buildInfoSection(),

                const SizedBox(height: 20),

                // Warning section
                _buildWarningSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackupButton({
    required String text,
    required IconData icon,
    required VoidCallback onTap,
    bool isLoading = false,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: const Color(0xFF3498DB),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              textDirection: TextDirection.rtl,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading) ...[
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                ] else ...[
                  Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                ],
                Text(
                  text,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info,
                color: Colors.white.withOpacity(0.8),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'معلومات مهمة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textDirection: TextDirection.rtl,
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoItem('💾', 'النسخ الاحتياطي يشمل جميع البيانات (العملاء، الموظفين، الديون، المدفوعات)'),
          _buildInfoItem('📁', 'يتم حفظ البيانات بصيغة JSON آمنة ومشفرة'),
          _buildInfoItem('🔄', 'يمكن استعادة البيانات كاملة أو جزئية حسب الحاجة'),
          _buildInfoItem('☁️', 'يُنصح بحفظ النسخ الاحتياطية في التخزين السحابي'),
          _buildInfoItem('⏰', 'يُنصح بعمل نسخة احتياطية أسبوعياً على الأقل'),
        ],
      ),
    );
  }

  Widget _buildWarningSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'تحذير مهم',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textDirection: TextDirection.rtl,
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildWarningItem('⚠️', 'استعادة البيانات ستحل محل البيانات الحالية'),
          _buildWarningItem('🔒', 'تأكد من صحة ملف النسخة الاحتياطية قبل الاستعادة'),
          _buildWarningItem('💡', 'يُنصح بعمل نسخة احتياطية قبل استعادة بيانات جديدة'),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 14,
              ),
              textDirection: TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 14,
              ),
              textDirection: TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _exportToFile() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // Show demo message for now
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إنشاء النسخة الاحتياطية بنجاح'),
          backgroundColor: Color(0xFF27AE60),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _importFromFile() async {
    setState(() {
      _isImporting = true;
    });

    try {
      // Show demo message for now
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم بدء عملية استعادة البيانات'),
          backgroundColor: Color(0xFF27AE60),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isImporting = false;
      });
    }
  }

  Future<void> _showPartialImportDialog() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'استعادة بيانات جزئية',
          textDirection: TextDirection.rtl,
        ),
        content: const Text(
          'اختر نوع البيانات التي تريد استعادتها:',
          textDirection: TextDirection.rtl,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _importPartialData('customers');
            },
            child: const Text('العملاء فقط'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _importPartialData('employees');
            },
            child: const Text('الموظفين فقط'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _importPartialData('debts');
            },
            child: const Text('الديون فقط'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Future<void> _importPartialData(String dataType) async {
    setState(() {
      _isPartialImporting = true;
    });

    try {
      // Show demo message for now
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم استعادة ${_getDataTypeDisplayName(dataType)} بنجاح'),
          backgroundColor: const Color(0xFF27AE60),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isPartialImporting = false;
      });
    }
  }

  String _getDataTypeDisplayName(String dataType) {
    switch (dataType) {
      case 'customers':
        return 'العملاء';
      case 'employees':
        return 'الموظفين';
      case 'debts':
        return 'الديون';
      default:
        return dataType;
    }
  }

  Future<bool> _showConfirmationDialog(String title, String content) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title, textDirection: TextDirection.rtl),
        content: Text(content, textDirection: TextDirection.rtl),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    ) ?? false;
  }
}
