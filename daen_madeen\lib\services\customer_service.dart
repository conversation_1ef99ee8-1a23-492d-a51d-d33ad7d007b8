import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/supabase_config.dart';
import '../models/models.dart';
import '../services/local_storage_service.dart';
import '../services/connectivity_service.dart';

/// Customer service for دائن مدين (Creditor-Debtor) system
/// Handles customer CRUD operations with offline support
class CustomerService {
  static final SupabaseClient _client = SupabaseConfig.client;
  static final ConnectivityService _connectivity = ConnectivityService();

  /// Get all customers for a business owner
  static Future<List<Customer>> getCustomersByBusinessOwner(String businessOwnerId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.customers
            .select()
            .eq('business_owner_id', businessOwnerId)
            .order('created_at', ascending: false);

        final customers = response.map((json) => Customer.fromJson(json)).toList();

        // Save to local storage
        for (final customer in customers) {
          await LocalStorageService.saveCustomer(customer);
        }

        return customers;
      } else {
        // Fetch from local storage
        final hiveCustomers = await LocalStorageService.getCustomersByBusinessOwner(businessOwnerId);
        return hiveCustomers.map((hive) => Customer(
          id: hive.id,
          businessOwnerId: hive.businessOwnerId,
          authUserId: hive.authUserId,
          name: hive.name,
          phone: hive.phone,
          username: hive.username,
          creditLimit: hive.creditLimit,
          currentBalance: hive.currentBalance,
          isActive: hive.isActive,
          createdAt: hive.createdAt,
          updatedAt: hive.updatedAt,
        )).toList();
      }
    } catch (e) {
      // Fallback to local storage on error
      final hiveCustomers = await LocalStorageService.getCustomersByBusinessOwner(businessOwnerId);
      return hiveCustomers.map((hive) => Customer(
        id: hive.id,
        businessOwnerId: hive.businessOwnerId,
        authUserId: hive.authUserId,
        name: hive.name,
        phone: hive.phone,
        username: hive.username,
        creditLimit: hive.creditLimit,
        currentBalance: hive.currentBalance,
        isActive: hive.isActive,
        createdAt: hive.createdAt,
        updatedAt: hive.updatedAt,
      )).toList();
    }
  }

  /// Get customer by ID
  static Future<Customer?> getCustomerById(String customerId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.customers
            .select()
            .eq('id', customerId)
            .maybeSingle();

        if (response != null) {
          final customer = Customer.fromJson(response);
          await LocalStorageService.saveCustomer(customer);
          return customer;
        }
      } else {
        // Fetch from local storage
        final hiveCustomer = await LocalStorageService.getCustomer(customerId);
        if (hiveCustomer != null) {
          return Customer(
            id: hiveCustomer.id,
            businessOwnerId: hiveCustomer.businessOwnerId,
            authUserId: hiveCustomer.authUserId,
            name: hiveCustomer.name,
            phone: hiveCustomer.phone,
            username: hiveCustomer.username,
            creditLimit: hiveCustomer.creditLimit,
            currentBalance: hiveCustomer.currentBalance,
            isActive: hiveCustomer.isActive,
            createdAt: hiveCustomer.createdAt,
            updatedAt: hiveCustomer.updatedAt,
          );
        }
      }
      return null;
    } catch (e) {
      // Fallback to local storage
      final hiveCustomer = await LocalStorageService.getCustomer(customerId);
      if (hiveCustomer != null) {
        return Customer(
          id: hiveCustomer.id,
          businessOwnerId: hiveCustomer.businessOwnerId,
          authUserId: hiveCustomer.authUserId,
          name: hiveCustomer.name,
          phone: hiveCustomer.phone,
          username: hiveCustomer.username,
          creditLimit: hiveCustomer.creditLimit,
          currentBalance: hiveCustomer.currentBalance,
          isActive: hiveCustomer.isActive,
          createdAt: hiveCustomer.createdAt,
          updatedAt: hiveCustomer.updatedAt,
        );
      }
      return null;
    }
  }

  /// Create a new customer
  static Future<Customer?> createCustomer({
    required String businessOwnerId,
    required String name,
    required String username,
    String? phone,
    double creditLimit = 0.0,
  }) async {
    try {
      // Check if username already exists
      final existingCustomer = await _checkUsernameExists(username);
      if (existingCustomer) {
        throw Exception('اسم المستخدم موجود بالفعل');
      }

      final customer = Customer.create(
        businessOwnerId: businessOwnerId,
        authUserId: '', // Will be set when auth account is created
        name: name,
        phone: phone,
        username: username,
        creditLimit: creditLimit,
      );

      if (_connectivity.isOnline) {
        // Insert to Supabase
        final response = await _client.customers
            .insert(customer.toInsertJson())
            .select()
            .single();

        final createdCustomer = Customer.fromJson(response);
        await LocalStorageService.saveCustomer(createdCustomer);
        return createdCustomer;
      } else {
        // Save to local storage only (will sync later)
        await LocalStorageService.saveCustomer(customer);
        return customer;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Update customer
  static Future<Customer?> updateCustomer(Customer customer) async {
    try {
      if (_connectivity.isOnline) {
        // Update in Supabase
        final response = await _client.customers
            .update(customer.toJson())
            .eq('id', customer.id)
            .select()
            .single();

        final updatedCustomer = Customer.fromJson(response);
        await LocalStorageService.saveCustomer(updatedCustomer);
        return updatedCustomer;
      } else {
        // Update in local storage only (will sync later)
        await LocalStorageService.saveCustomer(customer);
        return customer;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Delete customer
  static Future<bool> deleteCustomer(String customerId) async {
    try {
      if (_connectivity.isOnline) {
        // Delete from Supabase
        await _client.customers.delete().eq('id', customerId);
      }
      
      // Delete from local storage
      await LocalStorageService.deleteCustomer(customerId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Update customer balance
  static Future<Customer?> updateCustomerBalance(String customerId, double newBalance) async {
    try {
      final customer = await getCustomerById(customerId);
      if (customer != null) {
        final updatedCustomer = customer.copyWith(
          currentBalance: newBalance,
          updatedAt: DateTime.now(),
        );
        return await updateCustomer(updatedCustomer);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Check if username exists
  static Future<bool> _checkUsernameExists(String username) async {
    try {
      if (_connectivity.isOnline) {
        final response = await _client.customers
            .select('id')
            .eq('username', username)
            .maybeSingle();
        return response != null;
      } else {
        // Check in local storage
        final customers = await LocalStorageService.getAllBusinessOwners();
        // This is a simplified check - in production you'd need to check across all customers
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// Search customers by name or username
  static Future<List<Customer>> searchCustomers(String businessOwnerId, String query) async {
    try {
      final customers = await getCustomersByBusinessOwner(businessOwnerId);
      
      if (query.isEmpty) return customers;
      
      final lowercaseQuery = query.toLowerCase();
      return customers.where((customer) {
        return customer.name.toLowerCase().contains(lowercaseQuery) ||
               customer.username.toLowerCase().contains(lowercaseQuery);
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get customers with outstanding debts
  static Future<List<Customer>> getCustomersWithDebts(String businessOwnerId) async {
    try {
      final customers = await getCustomersByBusinessOwner(businessOwnerId);
      return customers.where((customer) => customer.currentBalance > 0).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get customers who exceeded credit limit
  static Future<List<Customer>> getCustomersOverCreditLimit(String businessOwnerId) async {
    try {
      final customers = await getCustomersByBusinessOwner(businessOwnerId);
      return customers.where((customer) => customer.hasExceededCreditLimit).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get customer statistics
  static Future<Map<String, dynamic>> getCustomerStatistics(String businessOwnerId) async {
    try {
      final customers = await getCustomersByBusinessOwner(businessOwnerId);
      
      final totalCustomers = customers.length;
      final activeCustomers = customers.where((c) => c.isActive).length;
      final customersWithDebts = customers.where((c) => c.currentBalance > 0).length;
      final customersOverLimit = customers.where((c) => c.hasExceededCreditLimit).length;
      final totalOutstandingDebt = customers.fold<double>(0, (sum, c) => sum + c.currentBalance);
      final totalCreditLimit = customers.fold<double>(0, (sum, c) => sum + c.creditLimit);

      return {
        'totalCustomers': totalCustomers,
        'activeCustomers': activeCustomers,
        'customersWithDebts': customersWithDebts,
        'customersOverLimit': customersOverLimit,
        'totalOutstandingDebt': totalOutstandingDebt,
        'totalCreditLimit': totalCreditLimit,
        'averageDebtPerCustomer': totalCustomers > 0 ? totalOutstandingDebt / totalCustomers : 0.0,
      };
    } catch (e) {
      return {
        'totalCustomers': 0,
        'activeCustomers': 0,
        'customersWithDebts': 0,
        'customersOverLimit': 0,
        'totalOutstandingDebt': 0.0,
        'totalCreditLimit': 0.0,
        'averageDebtPerCustomer': 0.0,
      };
    }
  }
}
