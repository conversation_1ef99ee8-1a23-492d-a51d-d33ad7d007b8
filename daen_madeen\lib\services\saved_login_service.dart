import 'package:hive/hive.dart';
import '../models/saved_login.dart';

class SavedLoginService {
  static const String _boxName = 'saved_logins';
  static SavedLoginService? _instance;
  Box<SavedLogin>? _box;

  SavedLoginService._();

  static SavedLoginService get instance {
    _instance ??= SavedLoginService._();
    return _instance!;
  }

  // Initialize the service
  Future<void> init() async {
    try {
      _box = await Hive.openBox<SavedLogin>(_boxName);
      print('✅ SavedLoginService initialized successfully');
    } catch (e) {
      print('❌ Error initializing SavedLoginService: $e');
      rethrow;
    }
  }

  // Get the box
  Box<SavedLogin> get box {
    if (_box == null || !_box!.isOpen) {
      throw Exception('SavedLoginService not initialized. Call init() first.');
    }
    return _box!;
  }

  // Save a new login
  Future<SavedLogin> saveLogin({
    required String storeName,
    required String username,
    required String password,
    String? storeDescription,
  }) async {
    try {
      // Check if login already exists for this store
      final existingLogin = getLoginByStoreName(storeName);
      if (existingLogin != null) {
        // Update existing login
        existingLogin.updateDetails(
          username: username,
          password: password,
          storeDescription: storeDescription,
        );
        existingLogin.updateLastUsed();
        return existingLogin;
      }

      // Create new login
      final savedLogin = SavedLogin.create(
        storeName: storeName,
        username: username,
        password: password,
        storeDescription: storeDescription,
      );

      await box.put(savedLogin.id, savedLogin);
      print('✅ Login saved for store: $storeName');
      return savedLogin;
    } catch (e) {
      print('❌ Error saving login: $e');
      rethrow;
    }
  }

  // Get all saved logins
  List<SavedLogin> getAllLogins() {
    try {
      final logins = box.values.toList();
      // Sort by last used (most recent first), then by favorites
      logins.sort((a, b) {
        if (a.isFavorite && !b.isFavorite) return -1;
        if (!a.isFavorite && b.isFavorite) return 1;
        return b.lastUsed.compareTo(a.lastUsed);
      });
      return logins;
    } catch (e) {
      print('❌ Error getting all logins: $e');
      return [];
    }
  }

  // Get login by store name
  SavedLogin? getLoginByStoreName(String storeName) {
    try {
      return box.values.firstWhere(
        (login) => login.storeName.toLowerCase() == storeName.toLowerCase(),
        orElse: () => throw StateError('Not found'),
      );
    } catch (e) {
      return null;
    }
  }

  // Get login by ID
  SavedLogin? getLoginById(String id) {
    try {
      return box.get(id);
    } catch (e) {
      print('❌ Error getting login by ID: $e');
      return null;
    }
  }

  // Update login
  Future<void> updateLogin(SavedLogin login) async {
    try {
      await login.save();
      print('✅ Login updated for store: ${login.storeName}');
    } catch (e) {
      print('❌ Error updating login: $e');
      rethrow;
    }
  }

  // Delete login
  Future<void> deleteLogin(String id) async {
    try {
      await box.delete(id);
      print('✅ Login deleted');
    } catch (e) {
      print('❌ Error deleting login: $e');
      rethrow;
    }
  }

  // Delete login by store name
  Future<void> deleteLoginByStoreName(String storeName) async {
    try {
      final login = getLoginByStoreName(storeName);
      if (login != null) {
        await deleteLogin(login.id);
      }
    } catch (e) {
      print('❌ Error deleting login by store name: $e');
      rethrow;
    }
  }

  // Search logins
  List<SavedLogin> searchLogins(String query) {
    try {
      if (query.isEmpty) return getAllLogins();
      
      final lowerQuery = query.toLowerCase();
      return box.values.where((login) {
        return login.storeName.toLowerCase().contains(lowerQuery) ||
               login.username.toLowerCase().contains(lowerQuery) ||
               (login.storeDescription?.toLowerCase().contains(lowerQuery) ?? false);
      }).toList();
    } catch (e) {
      print('❌ Error searching logins: $e');
      return [];
    }
  }

  // Get favorite logins
  List<SavedLogin> getFavoriteLogins() {
    try {
      return box.values.where((login) => login.isFavorite).toList();
    } catch (e) {
      print('❌ Error getting favorite logins: $e');
      return [];
    }
  }

  // Get recently used logins
  List<SavedLogin> getRecentLogins({int limit = 5}) {
    try {
      final logins = box.values.toList();
      logins.sort((a, b) => b.lastUsed.compareTo(a.lastUsed));
      return logins.take(limit).toList();
    } catch (e) {
      print('❌ Error getting recent logins: $e');
      return [];
    }
  }

  // Clear all logins
  Future<void> clearAllLogins() async {
    try {
      await box.clear();
      print('✅ All logins cleared');
    } catch (e) {
      print('❌ Error clearing all logins: $e');
      rethrow;
    }
  }

  // Get total count
  int getTotalCount() {
    try {
      return box.length;
    } catch (e) {
      print('❌ Error getting total count: $e');
      return 0;
    }
  }

  // Export logins (for backup)
  List<Map<String, dynamic>> exportLogins() {
    try {
      return box.values.map((login) => login.toMap()).toList();
    } catch (e) {
      print('❌ Error exporting logins: $e');
      return [];
    }
  }

  // Import logins (from backup)
  Future<void> importLogins(List<Map<String, dynamic>> loginMaps) async {
    try {
      for (final loginMap in loginMaps) {
        final login = SavedLogin.fromMap(loginMap);
        await box.put(login.id, login);
      }
      print('✅ Logins imported successfully');
    } catch (e) {
      print('❌ Error importing logins: $e');
      rethrow;
    }
  }
}
