import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';
import '../../widgets/dashboard_card.dart';
import '../../widgets/recent_activities_widget.dart';
import 'customers_screen.dart';
import 'debts_screen.dart';
import 'reports_screen.dart';

/// Business Owner Dashboard Screen for دائن مدين (Creditor-Debtor) system
class BusinessOwnerDashboardScreen extends StatefulWidget {
  const BusinessOwnerDashboardScreen({super.key});

  @override
  State<BusinessOwnerDashboardScreen> createState() => _BusinessOwnerDashboardScreenState();
}

class _BusinessOwnerDashboardScreenState extends State<BusinessOwnerDashboardScreen> {
  Map<String, dynamic> _customerStats = {};
  Map<String, dynamic> _debtStats = {};
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final businessOwner = authProvider.userProfile as BusinessOwner;

      // Load statistics
      final customerStats = await CustomerService.getCustomerStatistics(businessOwner.id);
      final debtStats = await DebtService.getDebtStatistics(businessOwner.id);

      setState(() {
        _customerStats = customerStats;
        _debtStats = debtStats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل البيانات: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم'),
        backgroundColor: const Color(0xFF0A0E27),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) async {
              if (value == 'logout') {
                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                await authProvider.signOut();
              } else if (value == 'profile') {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('الملف الشخصي قريباً')),
                );
              } else if (value == 'contact_us') {
                Navigator.pushNamed(context, '/contact_us_business');
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person),
                    SizedBox(width: 8),
                    Text('الملف الشخصي'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'contact_us',
                child: Row(
                  children: [
                    Icon(Icons.contact_support),
                    SizedBox(width: 8),
                    Text('تواصل معنا'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout),
                    SizedBox(width: 8),
                    Text('تسجيل الخروج'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator(color: Colors.white))
            : _errorMessage != null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _errorMessage!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadDashboardData,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF3B82F6),
                          ),
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  )
                : RefreshIndicator(
                    onRefresh: _loadDashboardData,
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Welcome message
                        Consumer<AuthProvider>(
                          builder: (context, authProvider, child) {
                            final businessOwner = authProvider.userProfile as BusinessOwner;
                            return Card(
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 30,
                                      backgroundColor: Colors.blue.shade100,
                                      child: Icon(
                                        Icons.business,
                                        size: 30,
                                        color: Colors.blue.shade800,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'مرحباً، ${businessOwner.ownerName}',
                                            style: const TextStyle(
                                              fontSize: 20,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            businessOwner.businessName,
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 20),

                        // Statistics cards
                        const Text(
                          'إحصائيات سريعة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        
                        // First row of stats
                        Row(
                          children: [
                            Expanded(
                              child: DashboardCard(
                                title: 'إجمالي العملاء',
                                value: '${_customerStats['totalCustomers'] ?? 0}',
                                icon: Icons.people,
                                color: Colors.blue,
                                onTap: () => _navigateToCustomers(),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: DashboardCard(
                                title: 'العملاء النشطون',
                                value: '${_customerStats['activeCustomers'] ?? 0}',
                                icon: Icons.person_outline,
                                color: Colors.green,
                                onTap: () => _navigateToCustomers(),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        
                        // Second row of stats
                        Row(
                          children: [
                            Expanded(
                              child: DashboardCard(
                                title: 'إجمالي الديون',
                                value: '${(_debtStats['totalAmount'] ?? 0.0).toStringAsFixed(2)}',
                                subtitle: 'ريال',
                                icon: Icons.account_balance_wallet,
                                color: Colors.orange,
                                onTap: () => _navigateToDebts(),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: DashboardCard(
                                title: 'الديون غير المدفوعة',
                                value: '${(_debtStats['unpaidAmount'] ?? 0.0).toStringAsFixed(2)}',
                                subtitle: 'ريال',
                                icon: Icons.warning,
                                color: Colors.red,
                                onTap: () => _navigateToDebts(),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // Quick actions
                        const Text(
                          'إجراءات سريعة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        
                        Row(
                          children: [
                            Expanded(
                              child: _buildQuickActionCard(
                                title: 'إضافة عميل جديد',
                                icon: Icons.person_add,
                                color: Colors.blue,
                                onTap: () => _showAddCustomerDialog(),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildQuickActionCard(
                                title: 'إضافة دين جديد',
                                icon: Icons.add_circle,
                                color: Colors.orange,
                                onTap: () => _showAddDebtDialog(),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        
                        Row(
                          children: [
                            Expanded(
                              child: _buildQuickActionCard(
                                title: 'تسجيل دفعة',
                                icon: Icons.payment,
                                color: Colors.green,
                                onTap: () => _showAddPaymentDialog(),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildQuickActionCard(
                                title: 'التقارير',
                                icon: Icons.analytics,
                                color: Colors.purple,
                                onTap: () => _navigateToReports(),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // Recent activities
                        const RecentActivitiesWidget(),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCustomers() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CustomersScreen()),
    );
  }

  void _navigateToDebts() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const DebtsScreen()),
    );
                      ),
                    ),
                  ),
      ),
    );
  }

  void _navigateToReports() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ReportsScreen()),
    );
  }

  void _showAddCustomerDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة عميل جديد قريباً')),
    );
  }

  void _showAddDebtDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة دين جديد قريباً')),
    );
  }

  void _showAddPaymentDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تسجيل دفعة قريباً')),
    );
  }
}
