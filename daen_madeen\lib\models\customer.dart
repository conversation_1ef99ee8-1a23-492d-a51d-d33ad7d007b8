import 'package:uuid/uuid.dart';

/// Customer model for دائن مدين (Creditor-Debtor) system
class Customer {
  final String id;
  final String businessOwnerId;
  final String authUserId;
  final String name;
  final String? phone;
  final String username;
  final double creditLimit;
  final double currentBalance;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Customer({
    required this.id,
    required this.businessOwnerId,
    required this.authUserId,
    required this.name,
    this.phone,
    required this.username,
    required this.creditLimit,
    required this.currentBalance,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Customer from JSON (from Supabase)
  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'] as String,
      businessOwnerId: json['business_owner_id'] as String,
      authUserId: json['auth_user_id'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String?,
      username: json['username'] as String,
      creditLimit: (json['credit_limit'] as num?)?.toDouble() ?? 0.0,
      currentBalance: (json['current_balance'] as num?)?.toDouble() ?? 0.0,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert Customer to JSON (for Supabase)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'business_owner_id': businessOwnerId,
      'auth_user_id': authUserId,
      'name': name,
      'phone': phone,
      'username': username,
      'credit_limit': creditLimit,
      'current_balance': currentBalance,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a new Customer for insertion (without timestamps)
  Map<String, dynamic> toInsertJson() {
    return {
      'business_owner_id': businessOwnerId,
      'auth_user_id': authUserId,
      'name': name,
      'phone': phone,
      'username': username,
      'credit_limit': creditLimit,
      'current_balance': currentBalance,
      'is_active': isActive,
    };
  }

  /// Create a copy with updated fields
  Customer copyWith({
    String? id,
    String? businessOwnerId,
    String? authUserId,
    String? name,
    String? phone,
    String? username,
    double? creditLimit,
    double? currentBalance,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Customer(
      id: id ?? this.id,
      businessOwnerId: businessOwnerId ?? this.businessOwnerId,
      authUserId: authUserId ?? this.authUserId,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      username: username ?? this.username,
      creditLimit: creditLimit ?? this.creditLimit,
      currentBalance: currentBalance ?? this.currentBalance,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create a Customer for new registration
  factory Customer.create({
    required String businessOwnerId,
    required String authUserId,
    required String name,
    String? phone,
    required String username,
    double creditLimit = 0.0,
    bool isActive = true,
  }) {
    final now = DateTime.now();
    return Customer(
      id: const Uuid().v4(),
      businessOwnerId: businessOwnerId,
      authUserId: authUserId,
      name: name,
      phone: phone,
      username: username,
      creditLimit: creditLimit,
      currentBalance: 0.0,
      isActive: isActive,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Check if customer has exceeded credit limit
  bool get hasExceededCreditLimit => currentBalance > creditLimit;

  /// Get remaining credit
  double get remainingCredit => creditLimit - currentBalance;

  /// Check if customer can take more debt
  bool canTakeDebt(double amount) => (currentBalance + amount) <= creditLimit;

  @override
  String toString() {
    return 'Customer(id: $id, name: $name, username: $username, balance: $currentBalance, limit: $creditLimit)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Customer && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
