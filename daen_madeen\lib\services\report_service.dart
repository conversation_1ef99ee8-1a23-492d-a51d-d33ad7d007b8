import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../models/models.dart';
import 'customer_service.dart';
import 'employee_service.dart';
import 'debt_service.dart';
import 'payment_service.dart';

/// Report service for generating various business reports
class ReportService {
  
  /// Generate customer report
  static Future<CustomerReportData> generateCustomerReport({
    required String customerId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Get customer details
      final customer = await CustomerService.getCustomerById(customerId);
      if (customer == null) {
        throw Exception('العميل غير موجود');
      }

      // Get customer debts
      final allDebts = await DebtService.getDebtsByCustomer(customerId);
      final debts = _filterByDateRange(allDebts, startDate, endDate);
      
      // Get customer payments
      final allPayments = await PaymentService.getPaymentsByCustomer(customerId);
      final payments = _filterPaymentsByDateRange(allPayments, startDate, endDate);

      // Calculate totals
      final totalDebts = debts.fold(0.0, (sum, debt) => sum + debt.amount);
      final totalPayments = payments.fold(0.0, (sum, payment) => sum + payment.amount);
      final remainingBalance = totalDebts - totalPayments;

      // Convert to report items
      final debtItems = debts.map((debt) => DebtReportItem(
        id: debt.id,
        description: debt.description ?? 'دين',
        amount: debt.amount,
        date: debt.createdAt,
        notes: debt.description,
      )).toList();

      final paymentItems = payments.map((payment) => PaymentReportItem(
        id: payment.id,
        amount: payment.amount,
        date: payment.createdAt,
        notes: payment.notes,
        method: payment.status.displayText,
      )).toList();

      // Find last transaction date
      DateTime? lastTransactionDate;
      if (debts.isNotEmpty || payments.isNotEmpty) {
        final allDates = [
          ...debts.map((d) => d.createdAt),
          ...payments.map((p) => p.createdAt),
        ];
        allDates.sort((a, b) => b.compareTo(a));
        lastTransactionDate = allDates.first;
      }

      return CustomerReportData(
        customerId: customer.id,
        customerName: customer.name,
        phone: customer.phone,
        totalDebts: totalDebts,
        totalPayments: totalPayments,
        remainingBalance: remainingBalance,
        debts: debtItems,
        payments: paymentItems,
        lastTransactionDate: lastTransactionDate,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Generate employee report
  static Future<EmployeeReportData> generateEmployeeReport({
    required String employeeId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Get employee details
      final employee = await EmployeeService.getEmployeeById(employeeId);
      if (employee == null) {
        throw Exception('الموظف غير موجود');
      }

      // Calculate working days
      final now = DateTime.now();
      final workingDays = now.difference(employee.hireDate).inDays;
      
      // Calculate total salary paid (simplified calculation)
      final monthsWorked = (workingDays / 30).floor();
      final totalSalaryPaid = monthsWorked * employee.salary;

      return EmployeeReportData(
        employeeId: employee.id,
        employeeName: employee.name,
        position: employee.position,
        salary: employee.salary,
        hireDate: employee.hireDate,
        phone: employee.phone,
        email: employee.email,
        isActive: employee.isActive,
        workingDays: workingDays,
        totalSalaryPaid: totalSalaryPaid,
        employmentDuration: employee.formattedEmploymentDuration,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Generate financial summary report
  static Future<FinancialSummaryData> generateFinancialSummary({
    required String businessOwnerId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Get all customers and employees
      final customers = await CustomerService.getCustomersByBusinessOwner(businessOwnerId);
      final employees = await EmployeeService.getEmployeesByBusinessOwner(businessOwnerId);

      // Get all debts and payments
      final allDebts = <Debt>[];
      final allPayments = <Payment>[];
      
      for (final customer in customers) {
        final customerDebts = await DebtService.getDebtsByCustomer(customer.id);
        final customerPayments = await PaymentService.getPaymentsByCustomer(customer.id);
        allDebts.addAll(customerDebts);
        allPayments.addAll(customerPayments);
      }

      // Filter by date range if specified
      final debts = _filterByDateRange(allDebts, startDate, endDate);
      final payments = _filterPaymentsByDateRange(allPayments, startDate, endDate);

      // Calculate totals
      final totalDebts = debts.fold(0.0, (sum, debt) => sum + debt.amount);
      final totalPayments = payments.fold(0.0, (sum, payment) => sum + payment.amount);
      final outstandingBalance = totalDebts - totalPayments;
      
      final activeEmployees = employees.where((e) => e.isActive).toList();
      final totalSalaries = activeEmployees.fold(0.0, (sum, emp) => sum + emp.salary);

      // Calculate revenue and expenses
      final totalRevenue = totalPayments;
      final totalExpenses = totalSalaries; // Simplified - only salary expenses
      final netProfit = totalRevenue - totalExpenses;

      return FinancialSummaryData(
        totalRevenue: totalRevenue,
        totalExpenses: totalExpenses,
        netProfit: netProfit,
        totalDebts: totalDebts,
        totalPayments: totalPayments,
        outstandingBalance: outstandingBalance,
        totalCustomers: customers.length,
        activeCustomers: customers.where((c) => c.isActive).length,
        totalEmployees: employees.length,
        activeEmployees: activeEmployees.length,
        totalSalaries: totalSalaries,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get all customers for business owner
  static Future<List<Customer>> getCustomersForReports(String businessOwnerId) async {
    return await CustomerService.getCustomersByBusinessOwner(businessOwnerId);
  }

  /// Get all employees for business owner
  static Future<List<Employee>> getEmployeesForReports(String businessOwnerId) async {
    return await EmployeeService.getEmployeesByBusinessOwner(businessOwnerId);
  }

  /// Get reports directory
  static Future<Directory> getReportsDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final reportsDir = Directory('${appDir.path}/reports');
    if (!await reportsDir.exists()) {
      await reportsDir.create(recursive: true);
    }
    return reportsDir;
  }

  /// Generate unique filename for report
  static String generateReportFilename({
    required String type,
    required String format,
    String? entityName,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final entityPart = entityName != null ? '_${entityName.replaceAll(' ', '_')}' : '';
    return '${type}_report${entityPart}_$timestamp.$format';
  }

  /// Get date range for period
  static Map<String, DateTime?> getDateRangeForPeriod(ReportPeriod period) {
    final now = DateTime.now();
    DateTime? startDate;
    DateTime? endDate = now;

    switch (period) {
      case ReportPeriod.today:
        startDate = DateTime(now.year, now.month, now.day);
        endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case ReportPeriod.thisWeek:
        final weekday = now.weekday;
        startDate = now.subtract(Duration(days: weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        break;
      case ReportPeriod.thisMonth:
        startDate = DateTime(now.year, now.month, 1);
        break;
      case ReportPeriod.thisYear:
        startDate = DateTime(now.year, 1, 1);
        break;
      case ReportPeriod.all:
        startDate = null;
        endDate = null;
        break;
      case ReportPeriod.custom:
        // Will be set by user
        startDate = null;
        endDate = null;
        break;
    }

    return {'startDate': startDate, 'endDate': endDate};
  }

  /// Filter debts by date range
  static List<Debt> _filterByDateRange(List<Debt> debts, DateTime? startDate, DateTime? endDate) {
    if (startDate == null && endDate == null) {
      return debts;
    }

    return debts.where((debt) {
      if (startDate != null && debt.createdAt.isBefore(startDate)) {
        return false;
      }
      if (endDate != null && debt.createdAt.isAfter(endDate)) {
        return false;
      }
      return true;
    }).toList();
  }

  /// Filter payments by date range
  static List<Payment> _filterPaymentsByDateRange(List<Payment> payments, DateTime? startDate, DateTime? endDate) {
    if (startDate == null && endDate == null) {
      return payments;
    }

    return payments.where((payment) {
      if (startDate != null && payment.createdAt.isBefore(startDate)) {
        return false;
      }
      if (endDate != null && payment.createdAt.isAfter(endDate)) {
        return false;
      }
      return true;
    }).toList();
  }

  /// Validate report parameters
  static String? validateReportParameters({
    required ReportType type,
    required ReportPeriod period,
    DateTime? startDate,
    DateTime? endDate,
    String? entityId,
  }) {
    // Check if entity ID is required
    if ((type == ReportType.customer || type == ReportType.employee) && 
        (entityId == null || entityId.isEmpty)) {
      return type == ReportType.customer 
          ? 'يرجى اختيار عميل'
          : 'يرجى اختيار موظف';
    }

    // Check custom date range
    if (period == ReportPeriod.custom) {
      if (startDate == null || endDate == null) {
        return 'يرجى تحديد تاريخ البداية والنهاية';
      }
      if (startDate.isAfter(endDate)) {
        return 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية';
      }
    }

    return null;
  }
}
