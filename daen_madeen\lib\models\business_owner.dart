import 'package:uuid/uuid.dart';

/// Business Owner model for دائن مدين (Creditor-Debtor) system
class BusinessOwner {
  final String id;
  final String authUserId;
  final String businessName;
  final String ownerName;
  final String email;
  final String? phone;
  final String? address;
  final String? profileImagePath;
  final DateTime createdAt;
  final DateTime updatedAt;

  BusinessOwner({
    required this.id,
    required this.authUserId,
    required this.businessName,
    required this.ownerName,
    required this.email,
    this.phone,
    this.address,
    this.profileImagePath,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create BusinessOwner from JSON (from Supabase)
  factory BusinessOwner.fromJson(Map<String, dynamic> json) {
    return BusinessOwner(
      id: json['id'] as String,
      authUserId: json['auth_user_id'] as String,
      businessName: json['business_name'] as String,
      ownerName: json['owner_name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      profileImagePath: json['profile_image_path'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert BusinessOwner to JSON (for Supabase)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'auth_user_id': authUserId,
      'business_name': businessName,
      'owner_name': ownerName,
      'email': email,
      'phone': phone,
      'address': address,
      'profile_image_path': profileImagePath,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a new BusinessOwner for insertion (without timestamps)
  Map<String, dynamic> toInsertJson() {
    return {
      'auth_user_id': authUserId,
      'business_name': businessName,
      'owner_name': ownerName,
      'email': email,
      'phone': phone,
      'address': address,
      'profile_image_path': profileImagePath,
    };
  }

  /// Create a copy with updated fields
  BusinessOwner copyWith({
    String? id,
    String? authUserId,
    String? businessName,
    String? ownerName,
    String? email,
    String? phone,
    String? address,
    String? profileImagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BusinessOwner(
      id: id ?? this.id,
      authUserId: authUserId ?? this.authUserId,
      businessName: businessName ?? this.businessName,
      ownerName: ownerName ?? this.ownerName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create a BusinessOwner for new registration
  factory BusinessOwner.create({
    required String authUserId,
    required String businessName,
    required String ownerName,
    required String email,
    String? phone,
    String? address,
  }) {
    final now = DateTime.now();
    return BusinessOwner(
      id: const Uuid().v4(),
      authUserId: authUserId,
      businessName: businessName,
      ownerName: ownerName,
      email: email,
      phone: phone,
      address: address,
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  String toString() {
    return 'BusinessOwner(id: $id, businessName: $businessName, ownerName: $ownerName, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BusinessOwner && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
