import 'dart:async';
import '../models/models.dart';

/// Demo Authentication service for دائن مدين (Creditor-Debtor) system
/// Provides mock authentication functionality when Supabase is not available
class DemoAuthService {
  static final Map<String, Map<String, dynamic>> _users = {};
  static String? _currentUserId;
  static dynamic _currentUserProfile;
  
  static final StreamController<DemoAuthChangeEvent> _authController =
      StreamController<DemoAuthChangeEvent>.broadcast();

  /// Get current authenticated user
  static String? get currentUserId => _currentUserId;

  /// Check if user is authenticated
  static bool get isAuthenticated => _currentUserId != null;

  /// Get current user profile
  static dynamic get currentUserProfile => _currentUserProfile;

  /// Auth state changes stream
  static Stream<DemoAuthChangeEvent> get authStateChanges => _authController.stream;

  /// Business Owner Registration (Demo)
  static Future<bool> registerBusinessOwner({
    required String email,
    required String password,
    required String businessName,
    required String ownerName,
    String? phone,
    String? address,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Check if email already exists
    if (_users.containsKey(email)) {
      throw Exception('البريد الإلكتروني مستخدم بالفعل');
    }

    // Create business owner
    final businessOwner = BusinessOwner.create(
      authUserId: 'demo_${DateTime.now().millisecondsSinceEpoch}',
      businessName: businessName,
      ownerName: ownerName,
      email: email,
      phone: phone,
      address: address,
    );

    // Store user
    _users[email] = {
      'password': password,
      'type': 'business_owner',
      'profile': businessOwner,
    };

    return true;
  }

  /// Customer Registration (Demo)
  static Future<bool> registerCustomer({
    required String businessOwnerId,
    required String username,
    required String password,
    required String name,
    String? phone,
    double creditLimit = 0.0,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    final email = '$<EMAIL>';

    // Check if username already exists
    if (_users.containsKey(email)) {
      throw Exception('اسم المستخدم مستخدم بالفعل');
    }

    // Create customer
    final customer = Customer.create(
      businessOwnerId: businessOwnerId,
      authUserId: 'demo_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      phone: phone,
      username: username,
      creditLimit: creditLimit,
    );

    // Store user
    _users[email] = {
      'password': password,
      'type': 'customer',
      'profile': customer,
    };

    return true;
  }

  /// Business Owner Login (Demo)
  static Future<bool> loginBusinessOwner({
    required String email,
    required String password,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    final user = _users[email];
    if (user == null) {
      throw Exception('البريد الإلكتروني غير مسجل');
    }

    if (user['password'] != password) {
      throw Exception('كلمة المرور غير صحيحة');
    }

    if (user['type'] != 'business_owner') {
      throw Exception('هذا الحساب ليس لصاحب عمل');
    }

    _currentUserId = user['profile'].authUserId;
    _currentUserProfile = user['profile'];
    _authController.add(DemoAuthChangeEvent.signedIn);

    return true;
  }

  /// Customer Login (Demo)
  static Future<bool> loginCustomer({
    required String username,
    required String password,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    final email = '$<EMAIL>';
    final user = _users[email];
    
    if (user == null) {
      throw Exception('اسم المستخدم غير مسجل');
    }

    if (user['password'] != password) {
      throw Exception('كلمة المرور غير صحيحة');
    }

    if (user['type'] != 'customer') {
      throw Exception('هذا الحساب ليس لعميل');
    }

    _currentUserId = user['profile'].authUserId;
    _currentUserProfile = user['profile'];
    _authController.add(DemoAuthChangeEvent.signedIn);

    return true;
  }

  /// Sign out (Demo)
  static Future<void> signOut() async {
    _currentUserId = null;
    _currentUserProfile = null;
    _authController.add(DemoAuthChangeEvent.signedOut);
  }

  /// Reset password (Demo)
  static Future<bool> resetBusinessOwnerPassword(String email) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    if (!_users.containsKey(email)) {
      throw Exception('البريد الإلكتروني غير مسجل');
    }

    // In a real app, this would send an email
    return true;
  }

  /// Get current user profile (Demo)
  static Future<dynamic> getCurrentUserProfile() async {
    return _currentUserProfile;
  }

  /// Check if current user is business owner
  static Future<bool> isBusinessOwner() async {
    return _currentUserProfile is BusinessOwner;
  }

  /// Check if current user is customer
  static Future<bool> isCustomer() async {
    return _currentUserProfile is Customer;
  }

  /// Initialize demo data
  static void initializeDemoData() {
    // Create the main business owner (عثمان الحمادي)
    final mainBusinessOwner = BusinessOwner.create(
      authUserId: 'demo_business_owner_1',
      businessName: 'محل عثمان الحمادي',
      ownerName: 'عثمان الحمادي',
      email: '<EMAIL>',
      phone: '**********',
      address: 'الرياض، المملكة العربية السعودية',
    );

    _users['<EMAIL>'] = {
      'password': '123456',
      'type': 'business_owner',
      'profile': mainBusinessOwner,
    };

    // Create the main customer (عثمان)
    final mainCustomer = Customer.create(
      businessOwnerId: mainBusinessOwner.id,
      authUserId: 'demo_customer_1',
      name: 'عثمان',
      phone: '**********',
      username: 'othman',
      creditLimit: 5000.0,
    );

    _users['<EMAIL>'] = {
      'password': '123456',
      'type': 'customer',
      'profile': mainCustomer,
    };

    // Keep the old demo accounts for testing
    final demoBusinessOwner = BusinessOwner.create(
      authUserId: 'demo_business_owner_2',
      businessName: 'محل الأمانة',
      ownerName: 'أحمد محمد',
      email: '<EMAIL>',
      phone: '**********',
      address: 'الرياض، المملكة العربية السعودية',
    );

    _users['<EMAIL>'] = {
      'password': '123456',
      'type': 'business_owner',
      'profile': demoBusinessOwner,
    };

    final demoCustomer = Customer.create(
      businessOwnerId: demoBusinessOwner.id,
      authUserId: 'demo_customer_2',
      name: 'سارة أحمد',
      phone: '**********',
      username: 'sara',
      creditLimit: 1000.0,
    );

    _users['<EMAIL>'] = {
      'password': '123456',
      'type': 'customer',
      'profile': demoCustomer,
    };
  }
}

/// Auth change events for demo
enum DemoAuthChangeEvent {
  signedIn,
  signedOut,
}
