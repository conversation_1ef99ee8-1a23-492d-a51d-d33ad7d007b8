# صفحات دخول الزبائن الاحترافية - Professional Customer Login Pages

## 🎉 الميزات الجديدة المضافة

### 📱 صفحة دخول احترافية للزبائن
- **الملف**: `lib/screens/customer/professional_customer_login_screen.dart`
- **المسار**: `/professional_customer_login`

#### ✨ المميزات:
- **تصميم احترافي** مع تدرجات لونية عصرية
- **رسوم متحركة سلسة** للعناصر والانتقالات
- **شريط إعلانات متحرك** في أعلى الصفحة
- **واجهة مستخدم تفاعلية** مع تأثيرات بصرية
- **تحقق من صحة البيانات** مع رسائل خطأ واضحة
- **خيار "تذكرني"** لحفظ بيانات الدخول
- **تصميم متجاوب** يعمل على جميع الأحجام

### 🏠 لوحة تحكم احترافية للزبائن
- **الملف**: `lib/screens/customer/professional_customer_dashboard_screen.dart`
- **المسار**: `/professional_customer_dashboard`

#### ✨ المميزات:
- **شريط إعلانات متحرك** مع عروض وإعلانات
- **ملخص مالي تفاعلي** مع إحصائيات ملونة
- **عرض الديون الحديثة** مع حالة كل دين
- **سجل المدفوعات** مع تواريخ وتفاصيل
- **رسوم متحركة متقدمة** للعناصر
- **تصميم بطاقات أنيق** مع ظلال وتأثيرات
- **قائمة جانبية** للإعدادات والخيارات

### 🎨 مكون شريط الإعلانات
- **الملف**: `lib/widgets/advertisement_banner.dart`

#### ✨ المميزات:
- **تمرير تلقائي** للإعلانات
- **مؤشرات الصفحات** التفاعلية
- **تأثيرات انتقال سلسة** بين الإعلانات
- **دعم الصور والنصوص** مع تدرجات خلفية
- **تحكم في السرعة والتوقيت**
- **تصميم قابل للتخصيص** بالكامل

## 🚀 كيفية الاستخدام

### 1. الوصول للصفحات الجديدة
```dart
// للانتقال لصفحة الدخول الاحترافية
Navigator.pushNamed(context, '/professional_customer_login');

// للانتقال للوحة التحكم الاحترافية
Navigator.pushNamed(context, '/professional_customer_dashboard');
```

### 2. استخدام شريط الإعلانات
```dart
AdvertisementBanner(
  advertisements: [
    AdvertisementItem(
      title: 'عرض خاص',
      subtitle: 'خصم 20% على جميع المنتجات',
      backgroundGradient: LinearGradient(
        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
      ),
    ),
  ],
  height: 120,
  autoScrollDuration: Duration(seconds: 4),
)
```

## 🎨 الألوان والتصميم

### لوحة الألوان الرئيسية:
- **الأزرق الداكن**: `#0A0E27`
- **الأزرق المتوسط**: `#1A1F3A`
- **الأزرق الفاتح**: `#2D3561`
- **البنفسجي**: `#667eea` إلى `#764ba2`
- **الأخضر**: `#43e97b`
- **الأحمر**: `#f5576c`

### التدرجات المستخدمة:
```dart
// التدرج الرئيسي
LinearGradient(
  colors: [Color(0xFF0A0E27), Color(0xFF1A1F3A), Color(0xFF2D3561)],
)

// تدرج الأزرار
LinearGradient(
  colors: [Color(0xFF667eea), Color(0xFF764ba2)],
)
```

## 📱 الرسوم المتحركة

### أنواع الرسوم المتحركة المستخدمة:
1. **FadeTransition** - للظهور والاختفاء التدريجي
2. **SlideTransition** - للحركة من الأسفل للأعلى
3. **ScaleTransition** - للتكبير والتصغير
4. **AnimatedContainer** - للتغييرات التدريجية

### مدة الرسوم المتحركة:
- **سريعة**: 300ms للتفاعلات الفورية
- **متوسطة**: 800ms للانتقالات العادية
- **بطيئة**: 1200ms للرسوم المتحركة المعقدة

## 🔧 التخصيص

### تخصيص شريط الإعلانات:
```dart
AdvertisementBanner(
  height: 140,                              // ارتفاع الشريط
  autoScrollDuration: Duration(seconds: 5), // مدة التمرير التلقائي
  transitionDuration: Duration(milliseconds: 600), // مدة الانتقال
  enableAutoScroll: true,                   // تفعيل التمرير التلقائي
  borderRadius: BorderRadius.circular(20), // زاوية الحواف
)
```

### تخصيص الألوان:
```dart
// في ملف الثيم أو الصفحة
const primaryGradient = LinearGradient(
  colors: [Color(0xFF667eea), Color(0xFF764ba2)],
);

const backgroundGradient = LinearGradient(
  colors: [Color(0xFF0A0E27), Color(0xFF1A1F3A), Color(0xFF2D3561)],
);
```

## 📊 الإحصائيات المعروضة

### في لوحة التحكم:
- **إجمالي الديون** - مجموع جميع الديون
- **المبلغ المتبقي** - المبلغ غير المدفوع
- **المدفوع** - إجمالي المبالغ المدفوعة
- **عدد الديون** - عدد الديون الإجمالي

### تنسيق العملة:
```dart
'${amount.toStringAsFixed(2)} ريال'
```

## 🔄 التحديثات المستقبلية

### ميزات مخططة:
- [ ] إضافة المزيد من أنواع الإعلانات
- [ ] تحسين الرسوم المتحركة
- [ ] إضافة ثيمات متعددة
- [ ] دعم اللغات المتعددة
- [ ] إضافة الإشعارات التفاعلية

## 🛠️ الملفات المحدثة

### ملفات جديدة:
1. `lib/widgets/advertisement_banner.dart`
2. `lib/screens/customer/professional_customer_login_screen.dart`
3. `lib/screens/customer/professional_customer_dashboard_screen.dart`

### ملفات محدثة:
1. `lib/main.dart` - إضافة المسارات الجديدة
2. `lib/screens/auth/login_screen.dart` - تحديث التنقل

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تواصل مع فريق التطوير
- راجع الوثائق التقنية
- استخدم نظام التذاكر الداخلي

---

**تم التطوير بواسطة**: فريق تطوير دائن مدين  
**التاريخ**: 2025-07-13  
**الإصدار**: 1.0.0
