import 'package:flutter/material.dart';
import '../../models/models.dart';
import 'add_employee_screen.dart';

/// Employee details screen for business owners
class EmployeeDetailsScreen extends StatelessWidget {
  final Employee employee;

  const EmployeeDetailsScreen({
    super.key,
    required this.employee,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(employee.name),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _editEmployee(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Employee header card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Avatar and basic info
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 40,
                          backgroundColor: employee.isActive 
                              ? Colors.green.shade100 
                              : Colors.grey.shade300,
                          child: Icon(
                            Icons.person,
                            size: 48,
                            color: employee.isActive 
                                ? Colors.green.shade700 
                                : Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                employee.name,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                                textDirection: TextDirection.rtl,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                employee.position,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey.shade600,
                                ),
                                textDirection: TextDirection.rtl,
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: employee.isActive 
                                      ? Colors.green.shade100 
                                      : Colors.red.shade100,
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  employee.isActive ? 'نشط' : 'غير نشط',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: employee.isActive 
                                        ? Colors.green.shade700 
                                        : Colors.red.shade700,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Employment information
            _buildSectionCard(
              title: 'معلومات التوظيف',
              icon: Icons.work,
              children: [
                _buildDetailRow('الراتب', employee.formattedSalary),
                _buildDetailRow('تاريخ التوظيف', employee.formattedHireDate),
                _buildDetailRow('مدة الخدمة', employee.formattedEmploymentDuration),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Contact information
            _buildSectionCard(
              title: 'معلومات الاتصال',
              icon: Icons.contact_phone,
              children: [
                if (employee.phone != null)
                  _buildDetailRow('رقم الهاتف', employee.phone!),
                if (employee.email != null)
                  _buildDetailRow('البريد الإلكتروني', employee.email!),
                if (employee.address != null)
                  _buildDetailRow('العنوان', employee.address!),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Personal information
            if (employee.nationalId != null)
              _buildSectionCard(
                title: 'المعلومات الشخصية',
                icon: Icons.badge,
                children: [
                  _buildDetailRow('رقم الهوية', employee.nationalId!),
                ],
              ),
            
            const SizedBox(height: 16),
            
            // Notes
            if (employee.notes != null && employee.notes!.isNotEmpty)
              _buildSectionCard(
                title: 'ملاحظات',
                icon: Icons.note,
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Text(
                      employee.notes!,
                      style: const TextStyle(fontSize: 14),
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _editEmployee(context),
        backgroundColor: Colors.blue.shade800,
        child: const Icon(Icons.edit, color: Colors.white),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: Colors.blue.shade700,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
              textDirection: TextDirection.rtl,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              textDirection: TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  void _editEmployee(BuildContext context) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEmployeeScreen(employee: employee),
      ),
    );
    
    if (result == true) {
      // Refresh the screen or navigate back
      Navigator.pop(context, true);
    }
  }
}
