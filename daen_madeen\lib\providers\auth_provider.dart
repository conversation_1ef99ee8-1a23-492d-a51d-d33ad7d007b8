import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/safe_auth_service.dart';
import '../services/demo_auth_service.dart';
import '../services/simple_auth_service.dart';
import '../services/local_storage_service.dart';
import '../models/models.dart';
import '../utils/supabase_config.dart';

/// Authentication state enum
enum AppAuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// User type enum
enum UserType {
  businessOwner,
  customer,
  unknown,
}

/// Authentication provider for دائن مدين (Creditor-Debtor) system
class AuthProvider extends ChangeNotifier {
  AppAuthState _state = AppAuthState.initial;
  UserType _userType = UserType.unknown;
  dynamic _userProfile; // BusinessOwner or Customer
  String? _errorMessage;
  bool _isDemo = false;

  // Getters
  AppAuthState get state => _state;
  UserType get userType => _userType;
  dynamic get userProfile => _userProfile;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _state == AppAuthState.authenticated;
  bool get isBusinessOwner => _userType == UserType.businessOwner;
  bool get isCustomer => _userType == UserType.customer;
  bool get isLoading => _state == AppAuthState.loading;
  bool get isDemo => _isDemo;

  /// Initialize auth provider
  Future<void> initialize() async {
    _setState(AppAuthState.loading);

    try {
      // Check if Supabase is available
      bool supabaseAvailable = false;
      try {
        final client = SupabaseConfig.client;
        supabaseAvailable = true;
      } catch (e) {
        supabaseAvailable = false;
      }

      if (supabaseAvailable) {
        try {
          if (SafeAuthService.isAuthenticated) {
            await _loadUserProfile();
          } else {
            _setState(AppAuthState.unauthenticated);
          }

          // Listen to auth state changes
          SafeAuthService.authStateChanges.listen((authState) {
            _handleAuthStateChange(authState);
          });
        } catch (e) {
          // Fallback to demo mode if Supabase fails
          _isDemo = true;
          DemoAuthService.initializeDemoData();
          _setState(AppAuthState.unauthenticated);
        }
      } else {
        // Initialize demo mode
        _isDemo = true;
        DemoAuthService.initializeDemoData();
        _setState(AppAuthState.unauthenticated);

        // Listen to demo auth state changes
        DemoAuthService.authStateChanges.listen((event) {
          _handleDemoAuthStateChange(event);
        });
      }
    } catch (e) {
      _setError('خطأ في تهيئة النظام: ${e.toString()}');
    }
  }

  /// Handle auth state changes
  void _handleAuthStateChange(AuthState authState) async {
    if (authState.event == AuthChangeEvent.signedIn) {
      await _loadUserProfile();
    } else if (authState.event == AuthChangeEvent.signedOut) {
      _clearUserData();
      _setState(AppAuthState.unauthenticated);
    }
  }

  /// Handle demo auth state changes
  void _handleDemoAuthStateChange(DemoAuthChangeEvent event) async {
    if (event == DemoAuthChangeEvent.signedIn) {
      await _loadDemoUserProfile();
    } else if (event == DemoAuthChangeEvent.signedOut) {
      _clearUserData();
      _setState(AppAuthState.unauthenticated);
    }
  }

  /// Load user profile after authentication
  Future<void> _loadUserProfile() async {
    try {
      final profile = await SafeAuthService.getCurrentUserProfile();

      if (profile != null) {
        _userProfile = profile;
        _userType = profile is BusinessOwner
            ? UserType.businessOwner
            : UserType.customer;
        _setState(AppAuthState.authenticated);
      } else {
        _clearUserData();
        _setState(AppAuthState.unauthenticated);
      }
    } catch (e) {
      _setError('خطأ في تحميل بيانات المستخدم: ${e.toString()}');
    }
  }

  /// Load demo user profile after authentication
  Future<void> _loadDemoUserProfile() async {
    try {
      final profile = await DemoAuthService.getCurrentUserProfile();

      if (profile != null) {
        _userProfile = profile;
        _userType = profile is BusinessOwner
            ? UserType.businessOwner
            : UserType.customer;
        _setState(AppAuthState.authenticated);
      } else {
        _clearUserData();
        _setState(AppAuthState.unauthenticated);
      }
    } catch (e) {
      _setError('خطأ في تحميل بيانات المستخدم: ${e.toString()}');
    }
  }

  /// Business owner registration
  Future<bool> registerBusinessOwner({
    required String email,
    required String password,
    required String businessName,
    required String ownerName,
    String? phone,
    String? address,
  }) async {
    _setState(AppAuthState.loading);

    try {
      if (_isDemo) {
        final response = await SimpleAuthService.registerBusinessOwner(
          email: email,
          password: password,
          businessName: businessName,
          ownerName: ownerName,
          phone: phone,
          address: address,
        );
        if (response.success) {
          _setState(AppAuthState.unauthenticated);
          return true;
        } else {
          _setError(response.error ?? 'فشل في إنشاء الحساب');
          return false;
        }
      } else {
        try {
          final response = await SafeAuthService.registerBusinessOwner(
            email: email,
            password: password,
            businessName: businessName,
            ownerName: ownerName,
            phone: phone,
            address: address,
          );

          if (response.user != null) {
            await _loadUserProfile();
            return true;
          } else {
            _setError('فشل في إنشاء الحساب');
            return false;
          }
        } catch (e) {
          _setError('خطأ في الاتصال بالخادم');
          return false;
        }
      }
    } catch (e) {
      _setError('خطأ في التسجيل: ${e.toString()}');
      return false;
    }
  }

  /// Customer registration (by business owner)
  Future<bool> registerCustomer({
    required String username,
    required String password,
    required String name,
    String? phone,
    double creditLimit = 0.0,
  }) async {
    if (!isBusinessOwner) {
      _setError('فقط أصحاب الأعمال يمكنهم إضافة عملاء');
      return false;
    }

    _setState(AppAuthState.loading);

    try {
      final businessOwner = _userProfile as BusinessOwner;

      // For demo mode, just return success
      // In real implementation, this would use SafeAuthService
      _setState(AppAuthState.authenticated);
      return true;
    } catch (e) {
      _setError('خطأ في إضافة العميل: ${e.toString()}');
      return false;
    }
  }

  /// Business owner login
  Future<bool> loginBusinessOwner({
    required String email,
    required String password,
  }) async {
    _setState(AppAuthState.loading);

    try {
      if (_isDemo) {
        final response = await SimpleAuthService.loginBusinessOwner(
          email: email,
          password: password,
        );
        if (response.success) {
          // Set demo user profile based on email
          if (email == '<EMAIL>') {
            _userProfile = BusinessOwner.create(
              authUserId: 'demo_user_1',
              businessName: 'محل عثمان الحمادي',
              ownerName: 'عثمان الحمادي',
              email: email,
              phone: '0501234567',
              address: 'الرياض، المملكة العربية السعودية',
            );
          } else {
            _userProfile = BusinessOwner.create(
              authUserId: 'demo_user_2',
              businessName: 'محل الأمانة',
              ownerName: 'أحمد محمد',
              email: email,
              phone: '0501234567',
              address: 'الرياض، المملكة العربية السعودية',
            );
          }
          _userType = UserType.businessOwner;
          _setState(AppAuthState.authenticated);
          return true;
        } else {
          _setError(response.error ?? 'فشل في تسجيل الدخول');
          return false;
        }
      } else {
        try {
          final response = await SafeAuthService.loginBusinessOwner(
            email: email,
            password: password,
          );

          if (response.user != null) {
            await _loadUserProfile();
            return true;
          } else {
            _setError('فشل في تسجيل الدخول');
            return false;
          }
        } catch (e) {
          _setError('خطأ في الاتصال بالخادم');
          return false;
        }
      }
    } catch (e) {
      _setError('خطأ في تسجيل الدخول: ${e.toString()}');
      return false;
    }
  }

  /// Customer login
  Future<bool> loginCustomer({
    required String username,
    required String password,
  }) async {
    _setState(AppAuthState.loading);

    try {
      if (_isDemo) {
        final response = await SimpleAuthService.loginCustomer(
          username: username,
          password: password,
        );
        if (response.success) {
          // Set demo customer profile based on username
          if (username == 'othman') {
            _userProfile = Customer.create(
              businessOwnerId: 'demo_business_1',
              authUserId: 'demo_customer_1',
              name: 'عثمان',
              phone: '0509876543',
              username: username,
              creditLimit: 5000.0,
            );
          } else {
            _userProfile = Customer.create(
              businessOwnerId: 'demo_business_1',
              authUserId: 'demo_customer_2',
              name: 'سارة أحمد',
              phone: '0509876543',
              username: username,
              creditLimit: 1000.0,
            );
          }
          _userType = UserType.customer;
          _setState(AppAuthState.authenticated);
          return true;
        } else {
          _setError(response.error ?? 'فشل في تسجيل الدخول');
          return false;
        }
      } else {
        try {
          final response = await SafeAuthService.loginCustomer(
            username: username,
            password: password,
          );

          if (response.user != null) {
            await _loadUserProfile();
            return true;
          } else {
            _setError('فشل في تسجيل الدخول');
            return false;
          }
        } catch (e) {
          _setError('خطأ في الاتصال بالخادم');
          return false;
        }
      }
    } catch (e) {
      _setError('خطأ في تسجيل الدخول: ${e.toString()}');
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    _setState(AppAuthState.loading);

    try {
      if (_isDemo) {
        await SimpleAuthService.signOut();
      } else {
        try {
          await SafeAuthService.signOut();
        } catch (e) {
          // Ignore sign out errors in demo mode
        }
      }
      _clearUserData();
      _setState(AppAuthState.unauthenticated);
    } catch (e) {
      _setError('خطأ في تسجيل الخروج: ${e.toString()}');
    }
  }

  /// Reset business owner password
  Future<bool> resetBusinessOwnerPassword(String email) async {
    _setState(AppAuthState.loading);

    try {
      if (_isDemo) {
        final response = await SimpleAuthService.resetPassword(email);
        _setState(AppAuthState.unauthenticated);
        return response.success;
      } else {
        try {
          await SafeAuthService.resetBusinessOwnerPassword(email);
          _setState(AppAuthState.unauthenticated);
          return true;
        } catch (e) {
          _setError('خطأ في إعادة تعيين كلمة المرور');
          return false;
        }
      }
    } catch (e) {
      _setError('خطأ في إعادة تعيين كلمة المرور: ${e.toString()}');
      return false;
    }
  }

  /// Update user profile
  Future<bool> updateProfile(dynamic updatedProfile) async {
    _setState(AppAuthState.loading);

    try {
      // For demo mode, just update locally
      // In real implementation, this would use SafeAuthService

      _userProfile = updatedProfile;
      _setState(AppAuthState.authenticated);
      return true;
    } catch (e) {
      _setError('خطأ في تحديث البيانات: ${e.toString()}');
      return false;
    }
  }

  /// Update user profile specifically for business owners
  Future<void> updateUserProfile(dynamic updatedProfile) async {
    try {
      print('🔄 AuthProvider: بدء تحديث الملف الشخصي...');
      print('📊 وضع التجريب: $_isDemo');

      if (_isDemo) {
        // في وضع التجريب، نحدث البيانات محلياً فقط
        print('📝 تحديث البيانات في الذاكرة...');
        _userProfile = updatedProfile;

        // حفظ البيانات في التخزين المحلي
        if (updatedProfile != null) {
          print('💾 حفظ البيانات في التخزين المحلي...');
          await LocalStorageService.saveBusinessOwner(updatedProfile);
          print('✅ تم حفظ البيانات في التخزين المحلي');
        }

        print('🔔 إرسال إشعار التحديث...');
        notifyListeners();
        print('✅ تم تحديث الملف الشخصي بنجاح');
      } else {
        // في التطبيق الحقيقي، سيتم حفظ البيانات في قاعدة البيانات
        // await SafeAuthService.updateBusinessOwner(updatedProfile);
        _userProfile = updatedProfile;

        // حفظ البيانات في التخزين المحلي أيضاً
        if (updatedProfile != null) {
          await LocalStorageService.saveBusinessOwner(updatedProfile);
        }

        notifyListeners();
      }
    } catch (e) {
      print('❌ خطأ في تحديث الملف الشخصي: $e');
      throw Exception('فشل في تحديث البيانات: $e');
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Set state and notify listeners
  void _setState(AppAuthState newState) {
    _state = newState;
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    _errorMessage = error;
    _state = AppAuthState.error;
    notifyListeners();
  }

  /// Clear user data
  void _clearUserData() {
    _userProfile = null;
    _userType = UserType.unknown;
    _errorMessage = null;
  }
}
