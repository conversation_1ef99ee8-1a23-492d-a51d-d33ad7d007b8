import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import '../models/models.dart';
import 'report_service.dart';

/// PDF service for generating and exporting PDF reports
class PdfService {
  
  /// Generate customer report PDF
  static Future<Uint8List> generateCustomerReportPdf({
    required CustomerReportData customerData,
    required String businessName,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final pdf = pw.Document();
    
    // Load Arabic font
    final arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
    final arabicBoldFont = await PdfGoogleFonts.notoSansArabicBold();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicFont,
          bold: arabicBoldFont,
        ),
        build: (pw.Context context) {
          return [
            // Header
            _buildPdfHeader(
              title: 'تقرير العميل',
              businessName: businessName,
              startDate: startDate,
              endDate: endDate,
            ),
            
            pw.SizedBox(height: 20),
            
            // Customer info
            _buildCustomerInfoSection(customerData),
            
            pw.SizedBox(height: 20),
            
            // Summary
            _buildCustomerSummarySection(customerData),
            
            pw.SizedBox(height: 20),
            
            // Debts table
            if (customerData.debts.isNotEmpty) ...[
              _buildSectionTitle('تفاصيل الديون'),
              pw.SizedBox(height: 10),
              _buildDebtsTable(customerData.debts),
              pw.SizedBox(height: 20),
            ],
            
            // Payments table
            if (customerData.payments.isNotEmpty) ...[
              _buildSectionTitle('تفاصيل المدفوعات'),
              pw.SizedBox(height: 10),
              _buildPaymentsTable(customerData.payments),
            ],
          ];
        },
      ),
    );

    return pdf.save();
  }

  /// Generate employee report PDF
  static Future<Uint8List> generateEmployeeReportPdf({
    required EmployeeReportData employeeData,
    required String businessName,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final pdf = pw.Document();
    
    // Load Arabic font
    final arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
    final arabicBoldFont = await PdfGoogleFonts.notoSansArabicBold();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicFont,
          bold: arabicBoldFont,
        ),
        build: (pw.Context context) {
          return [
            // Header
            _buildPdfHeader(
              title: 'تقرير الموظف',
              businessName: businessName,
              startDate: startDate,
              endDate: endDate,
            ),
            
            pw.SizedBox(height: 20),
            
            // Employee info
            _buildEmployeeInfoSection(employeeData),
            
            pw.SizedBox(height: 20),
            
            // Employment details
            _buildEmploymentDetailsSection(employeeData),
          ];
        },
      ),
    );

    return pdf.save();
  }

  /// Generate financial summary PDF
  static Future<Uint8List> generateFinancialSummaryPdf({
    required FinancialSummaryData summaryData,
    required String businessName,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final pdf = pw.Document();
    
    // Load Arabic font
    final arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
    final arabicBoldFont = await PdfGoogleFonts.notoSansArabicBold();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicFont,
          bold: arabicBoldFont,
        ),
        build: (pw.Context context) {
          return [
            // Header
            _buildPdfHeader(
              title: 'التقرير المالي الإجمالي',
              businessName: businessName,
              startDate: startDate,
              endDate: endDate,
            ),
            
            pw.SizedBox(height: 20),
            
            // Financial overview
            _buildFinancialOverviewSection(summaryData),
            
            pw.SizedBox(height: 20),
            
            // Business statistics
            _buildBusinessStatisticsSection(summaryData),
          ];
        },
      ),
    );

    return pdf.save();
  }

  /// Build PDF header
  static pw.Widget _buildPdfHeader({
    required String title,
    required String businessName,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                title,
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              pw.Text(
                businessName,
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'تاريخ الإنشاء: ${_formatDate(DateTime.now())}',
                style: const pw.TextStyle(fontSize: 12),
              ),
              if (startDate != null && endDate != null)
                pw.Text(
                  'الفترة: من ${_formatDate(startDate)} إلى ${_formatDate(endDate)}',
                  style: const pw.TextStyle(fontSize: 12),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build customer info section
  static pw.Widget _buildCustomerInfoSection(CustomerReportData customerData) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('معلومات العميل'),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildInfoRow('اسم العميل:', customerData.customerName),
              ),
              if (customerData.phone != null)
                pw.Expanded(
                  child: _buildInfoRow('رقم الهاتف:', customerData.phone!),
                ),
            ],
          ),
          if (customerData.lastTransactionDate != null)
            _buildInfoRow('آخر معاملة:', _formatDate(customerData.lastTransactionDate!)),
        ],
      ),
    );
  }

  /// Build customer summary section
  static pw.Widget _buildCustomerSummarySection(CustomerReportData customerData) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('ملخص الحساب'),
          pw.SizedBox(height: 10),
          _buildInfoRow('إجمالي الديون:', '${customerData.totalDebts.toStringAsFixed(2)} ر.س'),
          _buildInfoRow('إجمالي المدفوعات:', '${customerData.totalPayments.toStringAsFixed(2)} ر.س'),
          pw.Divider(color: PdfColors.grey400),
          _buildInfoRow(
            'الرصيد المتبقي:',
            '${customerData.remainingBalance.toStringAsFixed(2)} ر.س',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// Build employee info section
  static pw.Widget _buildEmployeeInfoSection(EmployeeReportData employeeData) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('معلومات الموظف'),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildInfoRow('اسم الموظف:', employeeData.employeeName),
              ),
              pw.Expanded(
                child: _buildInfoRow('المنصب:', employeeData.position),
              ),
            ],
          ),
          pw.Row(
            children: [
              if (employeeData.phone != null)
                pw.Expanded(
                  child: _buildInfoRow('رقم الهاتف:', employeeData.phone!),
                ),
              if (employeeData.email != null)
                pw.Expanded(
                  child: _buildInfoRow('البريد الإلكتروني:', employeeData.email!),
                ),
            ],
          ),
          _buildInfoRow('الحالة:', employeeData.isActive ? 'نشط' : 'غير نشط'),
        ],
      ),
    );
  }

  /// Build employment details section
  static pw.Widget _buildEmploymentDetailsSection(EmployeeReportData employeeData) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('تفاصيل التوظيف'),
          pw.SizedBox(height: 10),
          _buildInfoRow('تاريخ التوظيف:', _formatDate(employeeData.hireDate)),
          _buildInfoRow('مدة الخدمة:', employeeData.employmentDuration),
          _buildInfoRow('عدد أيام العمل:', '${employeeData.workingDays} يوم'),
          _buildInfoRow('الراتب الشهري:', '${employeeData.salary.toStringAsFixed(2)} ر.س'),
          pw.Divider(color: PdfColors.grey400),
          _buildInfoRow(
            'إجمالي الرواتب المدفوعة:',
            '${employeeData.totalSalaryPaid.toStringAsFixed(2)} ر.س',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// Build financial overview section
  static pw.Widget _buildFinancialOverviewSection(FinancialSummaryData summaryData) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue200),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('النظرة المالية العامة'),
          pw.SizedBox(height: 10),
          _buildInfoRow('إجمالي الإيرادات:', '${summaryData.totalRevenue.toStringAsFixed(2)} ر.س'),
          _buildInfoRow('إجمالي المصروفات:', '${summaryData.totalExpenses.toStringAsFixed(2)} ر.س'),
          pw.Divider(color: PdfColors.blue300),
          _buildInfoRow(
            'صافي الربح:',
            '${summaryData.netProfit.toStringAsFixed(2)} ر.س',
            isTotal: true,
          ),
          pw.SizedBox(height: 10),
          _buildInfoRow('إجمالي الديون:', '${summaryData.totalDebts.toStringAsFixed(2)} ر.س'),
          _buildInfoRow('إجمالي المدفوعات:', '${summaryData.totalPayments.toStringAsFixed(2)} ر.س'),
          _buildInfoRow('الرصيد المعلق:', '${summaryData.outstandingBalance.toStringAsFixed(2)} ر.س'),
        ],
      ),
    );
  }

  /// Build business statistics section
  static pw.Widget _buildBusinessStatisticsSection(FinancialSummaryData summaryData) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('إحصائيات العمل'),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text('العملاء', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                    _buildInfoRow('إجمالي العملاء:', '${summaryData.totalCustomers}'),
                    _buildInfoRow('العملاء النشطين:', '${summaryData.activeCustomers}'),
                  ],
                ),
              ),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text('الموظفين', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                    _buildInfoRow('إجمالي الموظفين:', '${summaryData.totalEmployees}'),
                    _buildInfoRow('الموظفين النشطين:', '${summaryData.activeEmployees}'),
                    _buildInfoRow('إجمالي الرواتب:', '${summaryData.totalSalaries.toStringAsFixed(2)} ر.س'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build debts table
  static pw.Widget _buildDebtsTable(List<DebtReportItem> debts) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('التاريخ', isHeader: true),
            _buildTableCell('الوصف', isHeader: true),
            _buildTableCell('المبلغ (ر.س)', isHeader: true),
            _buildTableCell('ملاحظات', isHeader: true),
          ],
        ),
        // Data rows
        ...debts.map((debt) => pw.TableRow(
          children: [
            _buildTableCell(_formatDate(debt.date)),
            _buildTableCell(debt.description),
            _buildTableCell(debt.amount.toStringAsFixed(2)),
            _buildTableCell(debt.notes ?? '-'),
          ],
        )),
        // Total row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.blue50),
          children: [
            _buildTableCell('الإجمالي', isHeader: true),
            _buildTableCell('', isHeader: true),
            _buildTableCell(
              debts.fold(0.0, (sum, debt) => sum + debt.amount).toStringAsFixed(2),
              isHeader: true,
            ),
            _buildTableCell('', isHeader: true),
          ],
        ),
      ],
    );
  }

  /// Build payments table
  static pw.Widget _buildPaymentsTable(List<PaymentReportItem> payments) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('التاريخ', isHeader: true),
            _buildTableCell('المبلغ (ر.س)', isHeader: true),
            _buildTableCell('طريقة الدفع', isHeader: true),
            _buildTableCell('ملاحظات', isHeader: true),
          ],
        ),
        // Data rows
        ...payments.map((payment) => pw.TableRow(
          children: [
            _buildTableCell(_formatDate(payment.date)),
            _buildTableCell(payment.amount.toStringAsFixed(2)),
            _buildTableCell(payment.method ?? '-'),
            _buildTableCell(payment.notes ?? '-'),
          ],
        )),
        // Total row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.green50),
          children: [
            _buildTableCell('الإجمالي', isHeader: true),
            _buildTableCell(
              payments.fold(0.0, (sum, payment) => sum + payment.amount).toStringAsFixed(2),
              isHeader: true,
            ),
            _buildTableCell('', isHeader: true),
            _buildTableCell('', isHeader: true),
          ],
        ),
      ],
    );
  }

  /// Build section title
  static pw.Widget _buildSectionTitle(String title) {
    return pw.Text(
      title,
      style: pw.TextStyle(
        fontSize: 16,
        fontWeight: pw.FontWeight.bold,
        color: PdfColors.blue800,
      ),
    );
  }

  /// Build info row
  static pw.Widget _buildInfoRow(String label, String value, {bool isTotal = false}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        children: [
          pw.SizedBox(
            width: 120,
            child: pw.Text(
              label,
              style: pw.TextStyle(
                fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
                fontSize: isTotal ? 14 : 12,
              ),
            ),
          ),
          pw.Expanded(
            child: pw.Text(
              value,
              style: pw.TextStyle(
                fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
                fontSize: isTotal ? 14 : 12,
                color: isTotal ? PdfColors.blue800 : PdfColors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build table cell
  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// Format date for display
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Save PDF to file
  static Future<File> savePdfToFile(Uint8List pdfBytes, String filename) async {
    final directory = await ReportService.getReportsDirectory();
    final file = File('${directory.path}/$filename');
    await file.writeAsBytes(pdfBytes);
    return file;
  }

  /// Share PDF file
  static Future<void> sharePdf(Uint8List pdfBytes, String filename) async {
    await Printing.sharePdf(bytes: pdfBytes, filename: filename);
  }

  /// Print PDF
  static Future<void> printPdf(Uint8List pdfBytes) async {
    await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdfBytes);
  }
}
