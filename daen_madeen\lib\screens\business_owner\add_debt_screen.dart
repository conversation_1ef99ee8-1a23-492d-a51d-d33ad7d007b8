import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:typed_data';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';

/// Professional debt registration screen
class AddDebtScreen extends StatefulWidget {
  const AddDebtScreen({super.key});

  @override
  State<AddDebtScreen> createState() => _AddDebtScreenState();
}

class _AddDebtScreenState extends State<AddDebtScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _searchController = TextEditingController();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  String _selectedType = 'customer'; // customer or employee
  String? _selectedPersonId;
  String _selectedPersonName = '';
  List<dynamic> _searchResults = [];
  bool _isSearching = false;
  bool _isLoading = false;
  List<Uint8List> _attachedImages = [];
  final ImagePicker _picker = ImagePicker();
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _searchController.addListener(_onSearchChanged);
  }
  
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    _searchController.dispose();
    super.dispose();
  }
  
  void _onSearchChanged() {
    if (_searchController.text.isEmpty) {
      setState(() {
        _searchResults.clear();
        _isSearching = false;
      });
      return;
    }
    
    setState(() {
      _isSearching = true;
    });
    
    _performSearch(_searchController.text);
  }
  
  Future<void> _performSearch(String query) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final businessOwner = authProvider.userProfile as BusinessOwner;
      
      List<dynamic> results = [];
      
      if (_selectedType == 'customer') {
        final customers = await CustomerService.getCustomersByBusinessOwner(businessOwner.id);
        results = customers.where((c) => 
          c.name.toLowerCase().contains(query.toLowerCase())
        ).toList();
      } else {
        final employees = await EmployeeService.getEmployeesByBusinessOwner(businessOwner.id);
        results = employees.where((e) => 
          e.name.toLowerCase().contains(query.toLowerCase())
        ).toList();
      }
      
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
      });
    }
  }
  
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );
      
      if (image != null) {
        final bytes = await image.readAsBytes();
        setState(() {
          _attachedImages.add(bytes);
        });
        
        // Show success feedback
        _showSnackBar('تم إرفاق الصورة بنجاح', Colors.green);
      }
    } catch (e) {
      _showSnackBar('خطأ في التقاط الصورة', Colors.red);
    }
  }
  
  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );
      
      if (image != null) {
        final bytes = await image.readAsBytes();
        setState(() {
          _attachedImages.add(bytes);
        });
        
        _showSnackBar('تم إرفاق الصورة بنجاح', Colors.green);
      }
    } catch (e) {
      _showSnackBar('خطأ في اختيار الصورة', Colors.red);
    }
  }
  
  void _removeImage(int index) {
    setState(() {
      _attachedImages.removeAt(index);
    });
    _showSnackBar('تم حذف الصورة', Colors.orange);
  }
  
  void _showImageOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'إضافة صورة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textDirection: TextDirection.rtl,
              ),
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt, color: Color(0xFF2196F3)),
              title: const Text('التقاط صورة', textDirection: TextDirection.rtl),
              onTap: () {
                Navigator.pop(context);
                _pickImage();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library, color: Color(0xFF4CAF50)),
              title: const Text('اختيار من المعرض', textDirection: TextDirection.rtl),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
  
  Future<void> _saveDebt() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedPersonId == null) {
      _showSnackBar('يرجى اختيار العميل أو الموظف', Colors.red);
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final businessOwner = authProvider.userProfile as BusinessOwner;
      
      final debt = Debt(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        customerId: _selectedType == 'customer' ? _selectedPersonId! : null,
        employeeId: _selectedType == 'employee' ? _selectedPersonId! : null,
        businessOwnerId: businessOwner.id,
        amount: double.parse(_amountController.text),
        description: _descriptionController.text,
        dateCreated: DateTime.now(),
        isPaid: false,
        attachedImages: _attachedImages,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      await DebtService.createDebt(
        customerId: debt.customerId,
        employeeId: debt.employeeId,
        amount: debt.amount,
        description: debt.description,
        attachedImages: debt.attachedImages,
      );
      
      // Success feedback with animation
      _showSuccessDialog();
      
    } catch (e) {
      _showSnackBar('خطأ في حفظ الدين: ${e.toString()}', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                color: Color(0xFF4CAF50),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'تم تسجيل الدين بنجاح',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 10),
            Text(
              'تم إضافة دين بقيمة ${_amountController.text} ريال',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Go back to previous screen
            },
            child: const Text('موافق'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              _resetForm();
            },
            child: const Text('إضافة دين آخر'),
          ),
        ],
      ),
    );
  }
  
  void _resetForm() {
    _amountController.clear();
    _descriptionController.clear();
    _searchController.clear();
    setState(() {
      _selectedPersonId = null;
      _selectedPersonName = '';
      _searchResults.clear();
      _attachedImages.clear();
    });
  }
  
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'تسجيل دين جديد',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF2D3561),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Card
                  _buildHeaderCard(),

                  const SizedBox(height: 20),

                  // Type Selection
                  _buildTypeSelection(),

                  const SizedBox(height: 20),

                  // Person Search
                  _buildPersonSearch(),

                  const SizedBox(height: 20),

                  // Amount Input
                  _buildAmountInput(),

                  const SizedBox(height: 20),

                  // Description Input
                  _buildDescriptionInput(),

                  const SizedBox(height: 20),

                  // Image Attachments
                  _buildImageSection(),

                  const SizedBox(height: 30),

                  // Save Button
                  _buildSaveButton(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }


