import 'package:hive_flutter/hive_flutter.dart';
import '../models/hive_models.dart';
import '../models/models.dart';
import '../models/saved_login.dart';

/// Local storage service using Hive for دائن مدين (Creditor-Debtor) system
/// Provides offline functionality and data caching
class LocalStorageService {
  // Box names
  static const String _businessOwnersBox = 'business_owners';
  static const String _customersBox = 'customers';
  static const String _employeesBox = 'employees';
  static const String _debtsBox = 'debts';
  static const String _paymentsBox = 'payments';
  static const String _notificationsBox = 'notifications';
  static const String _settingsBox = 'settings';

  // Lazy boxes for better performance
  static LazyBox<HiveBusinessOwner>? _businessOwnersLazyBox;
  static LazyBox<HiveCustomer>? _customersLazyBox;
  static LazyBox<HiveEmployee>? _employeesLazyBox;
  static LazyBox<HiveDebt>? _debtsLazyBox;
  static LazyBox<HivePayment>? _paymentsLazyBox;
  static LazyBox<HiveNotification>? _notificationsLazyBox;
  static Box? _settingsRegularBox;

  /// Initialize local storage
  static Future<void> initialize() async {
    // Register adapters
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(HiveBusinessOwnerAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(HiveCustomerAdapter());
    }
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(HiveDebtAdapter());
    }
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(HivePaymentAdapter());
    }
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(HiveNotificationAdapter());
    }
    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(HiveEmployeeAdapter());
    }
    if (!Hive.isAdapterRegistered(6)) {
      Hive.registerAdapter(SavedLoginAdapter());
    }

    // Open boxes
    _businessOwnersLazyBox = await Hive.openLazyBox<HiveBusinessOwner>(_businessOwnersBox);
    _customersLazyBox = await Hive.openLazyBox<HiveCustomer>(_customersBox);
    _employeesLazyBox = await Hive.openLazyBox<HiveEmployee>(_employeesBox);
    _debtsLazyBox = await Hive.openLazyBox<HiveDebt>(_debtsBox);
    _paymentsLazyBox = await Hive.openLazyBox<HivePayment>(_paymentsBox);
    _notificationsLazyBox = await Hive.openLazyBox<HiveNotification>(_notificationsBox);
    _settingsRegularBox = await Hive.openBox(_settingsBox);
  }

  /// Close all boxes
  static Future<void> close() async {
    await _businessOwnersLazyBox?.close();
    await _customersLazyBox?.close();
    await _employeesLazyBox?.close();
    await _debtsLazyBox?.close();
    await _paymentsLazyBox?.close();
    await _notificationsLazyBox?.close();
    await _settingsRegularBox?.close();
  }

  /// Clear all local data
  static Future<void> clearAll() async {
    await _businessOwnersLazyBox?.clear();
    await _customersLazyBox?.clear();
    await _debtsLazyBox?.clear();
    await _paymentsLazyBox?.clear();
    await _notificationsLazyBox?.clear();
    await _settingsRegularBox?.clear();
  }

  // Business Owner operations
  static Future<void> saveBusinessOwner(BusinessOwner businessOwner) async {
    final hiveBusinessOwner = HiveBusinessOwner.fromBusinessOwner(businessOwner);
    await _businessOwnersLazyBox?.put(businessOwner.id, hiveBusinessOwner);
  }

  static Future<HiveBusinessOwner?> getBusinessOwner(String id) async {
    return await _businessOwnersLazyBox?.get(id);
  }

  static Future<List<HiveBusinessOwner>> getAllBusinessOwners() async {
    final keys = _businessOwnersLazyBox?.keys ?? [];
    final List<HiveBusinessOwner> businessOwners = [];
    
    for (final key in keys) {
      final businessOwner = await _businessOwnersLazyBox?.get(key);
      if (businessOwner != null) {
        businessOwners.add(businessOwner);
      }
    }
    
    return businessOwners;
  }

  // Customer operations
  static Future<void> saveCustomer(Customer customer) async {
    final hiveCustomer = HiveCustomer.fromCustomer(customer);
    await _customersLazyBox?.put(customer.id, hiveCustomer);
  }

  static Future<HiveCustomer?> getCustomer(String id) async {
    return await _customersLazyBox?.get(id);
  }

  static Future<List<HiveCustomer>> getCustomersByBusinessOwner(String businessOwnerId) async {
    final keys = _customersLazyBox?.keys ?? [];
    final List<HiveCustomer> customers = [];
    
    for (final key in keys) {
      final customer = await _customersLazyBox?.get(key);
      if (customer != null && customer.businessOwnerId == businessOwnerId) {
        customers.add(customer);
      }
    }
    
    return customers;
  }

  static Future<void> deleteCustomer(String id) async {
    await _customersLazyBox?.delete(id);
  }

  // Debt operations
  static Future<void> saveDebt(Debt debt, {bool isSynced = true}) async {
    final hiveDebt = HiveDebt.fromDebt(debt, isSynced: isSynced);
    await _debtsLazyBox?.put(debt.id, hiveDebt);
  }

  static Future<HiveDebt?> getDebt(String id) async {
    return await _debtsLazyBox?.get(id);
  }

  static Future<List<HiveDebt>> getDebtsByCustomer(String customerId) async {
    final keys = _debtsLazyBox?.keys ?? [];
    final List<HiveDebt> debts = [];
    
    for (final key in keys) {
      final debt = await _debtsLazyBox?.get(key);
      if (debt != null && debt.customerId == customerId) {
        debts.add(debt);
      }
    }
    
    // Sort by date created (newest first)
    debts.sort((a, b) => b.dateCreated.compareTo(a.dateCreated));
    return debts;
  }

  static Future<List<HiveDebt>> getDebtsByBusinessOwner(String businessOwnerId) async {
    final keys = _debtsLazyBox?.keys ?? [];
    final List<HiveDebt> debts = [];
    
    for (final key in keys) {
      final debt = await _debtsLazyBox?.get(key);
      if (debt != null && debt.businessOwnerId == businessOwnerId) {
        debts.add(debt);
      }
    }
    
    // Sort by date created (newest first)
    debts.sort((a, b) => b.dateCreated.compareTo(a.dateCreated));
    return debts;
  }

  static Future<List<HiveDebt>> getUnsyncedDebts() async {
    final keys = _debtsLazyBox?.keys ?? [];
    final List<HiveDebt> unsyncedDebts = [];
    
    for (final key in keys) {
      final debt = await _debtsLazyBox?.get(key);
      if (debt != null && !debt.isSynced) {
        unsyncedDebts.add(debt);
      }
    }
    
    return unsyncedDebts;
  }

  static Future<void> deleteDebt(String id) async {
    await _debtsLazyBox?.delete(id);
  }

  // Payment operations
  static Future<void> savePayment(Payment payment, {bool isSynced = true}) async {
    final hivePayment = HivePayment.fromPayment(payment, isSynced: isSynced);
    await _paymentsLazyBox?.put(payment.id, hivePayment);
  }

  static Future<HivePayment?> getPayment(String id) async {
    return await _paymentsLazyBox?.get(id);
  }

  static Future<List<HivePayment>> getPaymentsByDebt(String debtId) async {
    final keys = _paymentsLazyBox?.keys ?? [];
    final List<HivePayment> payments = [];
    
    for (final key in keys) {
      final payment = await _paymentsLazyBox?.get(key);
      if (payment != null && payment.debtId == debtId) {
        payments.add(payment);
      }
    }
    
    // Sort by payment date (newest first)
    payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
    return payments;
  }

  static Future<List<HivePayment>> getPaymentsByCustomer(String customerId) async {
    final keys = _paymentsLazyBox?.keys ?? [];
    final List<HivePayment> payments = [];

    for (final key in keys) {
      final payment = await _paymentsLazyBox?.get(key);
      if (payment != null && payment.customerId == customerId) {
        payments.add(payment);
      }
    }

    // Sort by payment date (newest first)
    payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
    return payments;
  }

  static Future<List<HivePayment>> getPaymentsByBusinessOwner(String businessOwnerId) async {
    final keys = _paymentsLazyBox?.keys ?? [];
    final List<HivePayment> payments = [];

    for (final key in keys) {
      final payment = await _paymentsLazyBox?.get(key);
      if (payment != null && payment.businessOwnerId == businessOwnerId) {
        payments.add(payment);
      }
    }

    // Sort by payment date (newest first)
    payments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
    return payments;
  }

  static Future<List<HivePayment>> getUnsyncedPayments() async {
    final keys = _paymentsLazyBox?.keys ?? [];
    final List<HivePayment> unsyncedPayments = [];
    
    for (final key in keys) {
      final payment = await _paymentsLazyBox?.get(key);
      if (payment != null && !payment.isSynced) {
        unsyncedPayments.add(payment);
      }
    }
    
    return unsyncedPayments;
  }

  static Future<void> deletePayment(String id) async {
    await _paymentsLazyBox?.delete(id);
  }

  // Notification operations
  static Future<void> saveNotification(AppNotification notification, {bool isSynced = true}) async {
    final hiveNotification = HiveNotification.fromNotification(notification, isSynced: isSynced);
    await _notificationsLazyBox?.put(notification.id, hiveNotification);
  }

  static Future<List<HiveNotification>> getNotificationsByRecipient(String recipientId) async {
    final keys = _notificationsLazyBox?.keys ?? [];
    final List<HiveNotification> notifications = [];
    
    for (final key in keys) {
      final notification = await _notificationsLazyBox?.get(key);
      if (notification != null && notification.recipientId == recipientId) {
        notifications.add(notification);
      }
    }
    
    // Sort by created date (newest first)
    notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return notifications;
  }

  static Future<int> getUnreadNotificationCount(String recipientId) async {
    final notifications = await getNotificationsByRecipient(recipientId);
    return notifications.where((n) => !n.isRead).length;
  }

  static Future<void> markNotificationAsRead(String id) async {
    final notification = await _notificationsLazyBox?.get(id);
    if (notification != null) {
      notification.isRead = true;
      await _notificationsLazyBox?.put(id, notification);
    }
  }

  static Future<List<HiveNotification>> getNotificationsByBusinessOwner(String businessOwnerId) async {
    final keys = _notificationsLazyBox?.keys ?? [];
    final List<HiveNotification> notifications = [];

    for (final key in keys) {
      final notification = await _notificationsLazyBox?.get(key);
      if (notification != null && notification.businessOwnerId == businessOwnerId) {
        notifications.add(notification);
      }
    }

    // Sort by created date (newest first)
    notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return notifications;
  }

  static Future<void> deleteNotification(String id) async {
    await _notificationsLazyBox?.delete(id);
  }

  // Settings operations
  static Future<void> saveSetting(String key, dynamic value) async {
    await _settingsRegularBox?.put(key, value);
  }

  static dynamic getSetting(String key, {dynamic defaultValue}) {
    return _settingsRegularBox?.get(key, defaultValue: defaultValue);
  }

  static Future<void> deleteSetting(String key) async {
    await _settingsRegularBox?.delete(key);
  }

  // Sync status operations
  static Future<void> markDebtAsSynced(String id) async {
    final debt = await _debtsLazyBox?.get(id);
    if (debt != null) {
      debt.isSynced = true;
      await _debtsLazyBox?.put(id, debt);
    }
  }

  static Future<void> markPaymentAsSynced(String id) async {
    final payment = await _paymentsLazyBox?.get(id);
    if (payment != null) {
      payment.isSynced = true;
      await _paymentsLazyBox?.put(id, payment);
    }
  }

  static Future<void> markNotificationAsSynced(String id) async {
    final notification = await _notificationsLazyBox?.get(id);
    if (notification != null) {
      notification.isSynced = true;
      await _notificationsLazyBox?.put(id, notification);
    }
  }

  // Employee operations
  /// Save employee to local storage
  static Future<void> saveEmployee(Employee employee) async {
    final hiveEmployee = HiveEmployee.fromEmployee(employee, isSynced: true);
    await _employeesLazyBox?.put(employee.id, hiveEmployee);
  }

  /// Get employee by ID
  static Future<HiveEmployee?> getEmployee(String employeeId) async {
    return await _employeesLazyBox?.get(employeeId);
  }

  /// Get all employees for a business owner
  static Future<List<HiveEmployee>> getEmployeesByBusinessOwner(String businessOwnerId) async {
    final employees = <HiveEmployee>[];
    final keys = _employeesLazyBox?.keys ?? [];

    for (final key in keys) {
      final employee = await _employeesLazyBox?.get(key);
      if (employee != null && employee.businessOwnerId == businessOwnerId) {
        employees.add(employee);
      }
    }

    return employees;
  }

  /// Delete employee from local storage
  static Future<void> deleteEmployee(String employeeId) async {
    await _employeesLazyBox?.delete(employeeId);
  }

  /// Get all employees (for sync purposes)
  static Future<List<HiveEmployee>> getAllEmployees() async {
    final employees = <HiveEmployee>[];
    final keys = _employeesLazyBox?.keys ?? [];

    for (final key in keys) {
      final employee = await _employeesLazyBox?.get(key);
      if (employee != null) {
        employees.add(employee);
      }
    }

    return employees;
  }

  /// Clear all employees
  static Future<void> clearAllEmployees() async {
    await _employeesLazyBox?.clear();
  }

  /// Initialize demo employees data
  static Future<void> initializeDemoEmployees() async {
    try {
      // Check if demo employees already exist
      final existingEmployees = await getAllEmployees();
      if (existingEmployees.isNotEmpty) {
        return; // Demo data already exists
      }

      // Create demo employees for the main business owner
      final businessOwnerId = 'demo_business_owner_1'; // Same as in DemoAuthService

      final demoEmployees = [
        Employee.create(
          businessOwnerId: businessOwnerId,
          authUserId: 'demo_emp_1',
          name: 'أحمد محمد',
          position: 'مدير المبيعات',
          salary: 8000.0,
          phone: '0501111111',
          email: '<EMAIL>',
          address: 'الرياض',
          nationalId: '1234567890',
          hireDate: DateTime.now().subtract(const Duration(days: 730)), // 2 years ago
          notes: 'موظف متميز في المبيعات',
        ),
        Employee.create(
          businessOwnerId: businessOwnerId,
          authUserId: 'demo_emp_2',
          name: 'فاطمة علي',
          position: 'محاسبة',
          salary: 6500.0,
          phone: '0502222222',
          email: '<EMAIL>',
          address: 'جدة',
          nationalId: '2345678901',
          hireDate: DateTime.now().subtract(const Duration(days: 365)), // 1 year ago
          notes: 'خبيرة في المحاسبة والمالية',
        ),
        Employee.create(
          businessOwnerId: businessOwnerId,
          authUserId: 'demo_emp_3',
          name: 'خالد السعد',
          position: 'مندوب مبيعات',
          salary: 4500.0,
          phone: '0503333333',
          address: 'الدمام',
          nationalId: '3456789012',
          hireDate: DateTime.now().subtract(const Duration(days: 180)), // 6 months ago
          notes: 'نشيط في التسويق الميداني',
        ),
        Employee.create(
          businessOwnerId: businessOwnerId,
          authUserId: '', // No username yet
          name: 'سارة أحمد',
          position: 'خدمة عملاء',
          salary: 3500.0,
          phone: '0504444444',
          email: '<EMAIL>',
          address: 'مكة المكرمة',
          hireDate: DateTime.now().subtract(const Duration(days: 90)), // 3 months ago
          notes: 'ممتازة في التعامل مع العملاء',
        ),
        Employee.create(
          businessOwnerId: businessOwnerId,
          authUserId: '', // No username yet
          name: 'محمد الغامدي',
          position: 'مدير المخزن',
          salary: 5500.0,
          phone: '0505555555',
          address: 'الطائف',
          nationalId: '4567890123',
          hireDate: DateTime.now().subtract(const Duration(days: 45)), // 1.5 months ago
          notes: 'خبرة واسعة في إدارة المخازن',
        ),
        Employee.create(
          businessOwnerId: businessOwnerId,
          authUserId: 'demo_emp_6',
          name: 'نورا الحربي',
          position: 'محاسبة',
          salary: 6000.0,
          phone: '0506666666',
          email: '<EMAIL>',
          address: 'الرياض',
          hireDate: DateTime.now().subtract(const Duration(days: 1095)), // 3 years ago
          isActive: false, // Inactive employee
          notes: 'موظفة سابقة - استقالت',
        ),
      ];

      // Save demo employees
      for (final employee in demoEmployees) {
        final hiveEmployee = HiveEmployee.fromEmployee(employee, isSynced: true);
        await _employeesLazyBox?.put(employee.id, hiveEmployee);
      }

      print('✅ تم إنشاء بيانات الموظفين التجريبية');
    } catch (e) {
      print('❌ خطأ في إنشاء بيانات الموظفين التجريبية: $e');
    }
  }
}
