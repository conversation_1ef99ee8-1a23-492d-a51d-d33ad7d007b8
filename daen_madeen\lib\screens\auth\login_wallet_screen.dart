import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/saved_login.dart';
import '../../services/saved_login_service.dart';

/// Login Wallet Screen - Manages saved login credentials for multiple stores
class LoginWalletScreen extends StatefulWidget {
  final Function(String username, String password)? onLoginSelected;
  
  const LoginWalletScreen({
    super.key,
    this.onLoginSelected,
  });

  @override
  State<LoginWalletScreen> createState() => _LoginWalletScreenState();
}

class _LoginWalletScreenState extends State<LoginWalletScreen>
    with TickerProviderStateMixin {
  final SavedLoginService _loginService = SavedLoginService.instance;
  final TextEditingController _searchController = TextEditingController();
  
  List<SavedLogin> _logins = [];
  List<SavedLogin> _filteredLogins = [];
  bool _isLoading = true;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadLogins();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadLogins() async {
    try {
      setState(() => _isLoading = true);
      _logins = _loginService.getAllLogins();
      _filteredLogins = _logins;
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: ${e.toString()}');
    }
  }

  void _filterLogins(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredLogins = _logins;
      } else {
        _filteredLogins = _logins.where((login) {
          return login.storeName.toLowerCase().contains(query.toLowerCase()) ||
                 login.username.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  void _showAddLoginDialog() {
    final storeNameController = TextEditingController();
    final usernameController = TextEditingController();
    final passwordController = TextEditingController();
    final descriptionController = TextEditingController();
    bool obscurePassword = true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text(
            'إضافة بيانات تسجيل دخول جديدة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: storeNameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المحل *',
                    hintText: 'مثال: محل أحمد للمواد الغذائية',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.store),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: usernameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المستخدم *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: passwordController,
                  obscureText: obscurePassword,
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور *',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(obscurePassword ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setDialogState(() {
                          obscurePassword = !obscurePassword;
                        });
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'وصف (اختياري)',
                    hintText: 'مثال: محل البقالة في الحي',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (storeNameController.text.trim().isEmpty ||
                    usernameController.text.trim().isEmpty ||
                    passwordController.text.trim().isEmpty) {
                  _showErrorSnackBar('يرجى ملء جميع الحقول المطلوبة');
                  return;
                }

                try {
                  await _loginService.saveLogin(
                    storeName: storeNameController.text.trim(),
                    username: usernameController.text.trim(),
                    password: passwordController.text.trim(),
                    storeDescription: descriptionController.text.trim().isEmpty 
                        ? null : descriptionController.text.trim(),
                  );
                  
                  Navigator.pop(context);
                  _loadLogins();
                  _showSuccessSnackBar('تم حفظ بيانات التسجيل بنجاح');
                } catch (e) {
                  _showErrorSnackBar('خطأ في الحفظ: ${e.toString()}');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3B82F6),
                foregroundColor: Colors.white,
              ),
              child: const Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  void _selectLogin(SavedLogin login) {
    login.updateLastUsed();
    if (widget.onLoginSelected != null) {
      widget.onLoginSelected!(login.username, login.password);
      Navigator.pop(context);
    }
  }

  void _showLoginOptions(SavedLogin login) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              login.storeName,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.login, color: Color(0xFF10B981)),
              title: const Text('استخدام بيانات التسجيل'),
              onTap: () {
                Navigator.pop(context);
                _selectLogin(login);
              },
            ),
            ListTile(
              leading: Icon(
                login.isFavorite ? Icons.favorite : Icons.favorite_border,
                color: Colors.red,
              ),
              title: Text(login.isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'),
              onTap: () {
                Navigator.pop(context);
                login.toggleFavorite();
                _loadLogins();
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy, color: Color(0xFF3B82F6)),
              title: const Text('نسخ اسم المستخدم'),
              onTap: () {
                Navigator.pop(context);
                Clipboard.setData(ClipboardData(text: login.username));
                _showSuccessSnackBar('تم نسخ اسم المستخدم');
              },
            ),
            ListTile(
              leading: const Icon(Icons.edit, color: Color(0xFFF59E0B)),
              title: const Text('تعديل'),
              onTap: () {
                Navigator.pop(context);
                _showEditLoginDialog(login);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('حذف'),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmation(login);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showEditLoginDialog(SavedLogin login) {
    final storeNameController = TextEditingController(text: login.storeName);
    final usernameController = TextEditingController(text: login.username);
    final passwordController = TextEditingController(text: login.password);
    final descriptionController = TextEditingController(text: login.storeDescription ?? '');
    bool obscurePassword = true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text(
            'تعديل بيانات التسجيل',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: storeNameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المحل *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.store),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: usernameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المستخدم *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: passwordController,
                  obscureText: obscurePassword,
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور *',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(obscurePassword ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setDialogState(() {
                          obscurePassword = !obscurePassword;
                        });
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'وصف (اختياري)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (storeNameController.text.trim().isEmpty ||
                    usernameController.text.trim().isEmpty ||
                    passwordController.text.trim().isEmpty) {
                  _showErrorSnackBar('يرجى ملء جميع الحقول المطلوبة');
                  return;
                }

                try {
                  login.updateDetails(
                    storeName: storeNameController.text.trim(),
                    username: usernameController.text.trim(),
                    password: passwordController.text.trim(),
                    storeDescription: descriptionController.text.trim().isEmpty 
                        ? null : descriptionController.text.trim(),
                  );
                  
                  Navigator.pop(context);
                  _loadLogins();
                  _showSuccessSnackBar('تم تحديث البيانات بنجاح');
                } catch (e) {
                  _showErrorSnackBar('خطأ في التحديث: ${e.toString()}');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3B82F6),
                foregroundColor: Colors.white,
              ),
              child: const Text('حفظ التغييرات'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(SavedLogin login) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف بيانات تسجيل الدخول لـ "${login.storeName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await _loginService.deleteLogin(login.id);
                Navigator.pop(context);
                _loadLogins();
                _showSuccessSnackBar('تم حذف البيانات بنجاح');
              } catch (e) {
                _showErrorSnackBar('خطأ في الحذف: ${e.toString()}');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const Expanded(
                      child: Text(
                        'حافظة تسجيل الدخول',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    IconButton(
                      onPressed: _showAddLoginDialog,
                      icon: const Icon(
                        Icons.add,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),

              // Search bar
              if (_logins.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: TextField(
                    controller: _searchController,
                    onChanged: _filterLogins,
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'البحث في المحلات المحفوظة...',
                      hintStyle: const TextStyle(color: Colors.white60),
                      prefixIcon: const Icon(Icons.search, color: Colors.white60),
                      filled: true,
                      fillColor: Colors.white.withOpacity(0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 20),

              // Content
              Expanded(
                child: _isLoading
                    ? const Center(
                        child: CircularProgressIndicator(color: Colors.white),
                      )
                    : _filteredLogins.isEmpty
                        ? _buildEmptyState()
                        : _buildLoginsList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _logins.isEmpty ? Icons.wallet : Icons.search_off,
              size: 80,
              color: Colors.white.withOpacity(0.5),
            ),
            const SizedBox(height: 20),
            Text(
              _logins.isEmpty 
                  ? 'لا توجد بيانات محفوظة'
                  : 'لا توجد نتائج للبحث',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              _logins.isEmpty
                  ? 'اضغط على + لإضافة بيانات تسجيل دخول جديدة'
                  : 'جرب البحث بكلمات أخرى',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (_logins.isEmpty) ...[
              const SizedBox(height: 30),
              ElevatedButton.icon(
                onPressed: _showAddLoginDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة بيانات جديدة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3B82F6),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoginsList() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          itemCount: _filteredLogins.length,
          itemBuilder: (context, index) {
            final login = _filteredLogins[index];
            return _buildLoginCard(login);
          },
        ),
      ),
    );
  }

  Widget _buildLoginCard(SavedLogin login) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: const Color(0xFF3B82F6),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.store,
            color: Colors.white,
            size: 24,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                login.storeName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            if (login.isFavorite)
              const Icon(
                Icons.favorite,
                color: Colors.red,
                size: 20,
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              'المستخدم: ${login.username}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
            if (login.storeDescription != null) ...[
              const SizedBox(height: 2),
              Text(
                login.storeDescription!,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withOpacity(0.6),
                ),
              ),
            ],
            const SizedBox(height: 4),
            Text(
              'آخر استخدام: ${_formatDate(login.lastUsed)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.white.withOpacity(0.5),
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _selectLogin(login),
              icon: const Icon(
                Icons.login,
                color: Color(0xFF10B981),
              ),
              tooltip: 'استخدام',
            ),
            IconButton(
              onPressed: () => _showLoginOptions(login),
              icon: const Icon(
                Icons.more_vert,
                color: Colors.white,
              ),
              tooltip: 'خيارات',
            ),
          ],
        ),
        onTap: () => _selectLogin(login),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return 'منذ ${difference.inMinutes} دقيقة';
      }
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
