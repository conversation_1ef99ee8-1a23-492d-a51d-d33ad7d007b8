import 'package:flutter/foundation.dart';
import 'supabase_config.dart';

/// أداة فحص الاتصال بـ Supabase
class ConnectionChecker {
  /// فحص حالة إعدادات Supabase
  static ConnectionStatus checkSupabaseConfig() {
    try {
      // فحص إذا كانت القيم لا تزال placeholder
      if (SupabaseConfig.supabaseUrl.contains('your-project-id') ||
          SupabaseConfig.supabaseAnonKey.contains('your-anon-key')) {
        return ConnectionStatus.notConfigured;
      }

      // فحص صحة تنسيق URL
      if (!SupabaseConfig.supabaseUrl.startsWith('https://') ||
          !SupabaseConfig.supabaseUrl.contains('.supabase.co')) {
        return ConnectionStatus.invalidUrl;
      }

      // فحص صحة تنسيق Anon Key
      if (!SupabaseConfig.supabaseAnonKey.startsWith('eyJ')) {
        return ConnectionStatus.invalidKey;
      }

      return ConnectionStatus.configured;
    } catch (e) {
      return ConnectionStatus.error;
    }
  }

  /// اختبار الاتصال بـ Supabase
  static Future<ConnectionStatus> testConnection() async {
    try {
      final configStatus = checkSupabaseConfig();
      if (configStatus != ConnectionStatus.configured) {
        return configStatus;
      }

      // محاولة الحصول على client
      final client = SupabaseConfig.client;
      
      // اختبار بسيط للاتصال
      await client.from('business_owners').select('id').limit(1);
      
      return ConnectionStatus.connected;
    } catch (e) {
      if (kDebugMode) {
        print('Connection test failed: $e');
      }
      return ConnectionStatus.connectionFailed;
    }
  }

  /// الحصول على رسالة الحالة بالعربية
  static String getStatusMessage(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.notConfigured:
        return 'Supabase غير مُعدّ. يرجى تحديث الإعدادات في supabase_config.dart';
      case ConnectionStatus.invalidUrl:
        return 'رابط Supabase غير صحيح. تأكد من تنسيق URL';
      case ConnectionStatus.invalidKey:
        return 'مفتاح Supabase غير صحيح. تأكد من Anon Key';
      case ConnectionStatus.configured:
        return 'الإعدادات صحيحة. جاري اختبار الاتصال...';
      case ConnectionStatus.connected:
        return 'تم الاتصال بـ Supabase بنجاح! 🎉';
      case ConnectionStatus.connectionFailed:
        return 'فشل في الاتصال بـ Supabase. تحقق من الإعدادات والإنترنت';
      case ConnectionStatus.error:
        return 'خطأ في فحص الإعدادات';
    }
  }

  /// الحصول على لون الحالة
  static String getStatusColor(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return 'green';
      case ConnectionStatus.configured:
        return 'blue';
      case ConnectionStatus.notConfigured:
      case ConnectionStatus.invalidUrl:
      case ConnectionStatus.invalidKey:
        return 'orange';
      case ConnectionStatus.connectionFailed:
      case ConnectionStatus.error:
        return 'red';
    }
  }

  /// الحصول على أيقونة الحالة
  static String getStatusIcon(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return '✅';
      case ConnectionStatus.configured:
        return '🔄';
      case ConnectionStatus.notConfigured:
        return '⚙️';
      case ConnectionStatus.invalidUrl:
      case ConnectionStatus.invalidKey:
        return '⚠️';
      case ConnectionStatus.connectionFailed:
      case ConnectionStatus.error:
        return '❌';
    }
  }

  /// طباعة تقرير مفصل عن الحالة
  static void printDetailedReport() {
    if (kDebugMode) {
      print('\n' + '='*50);
      print('📊 تقرير حالة Supabase');
      print('='*50);
      
      final configStatus = checkSupabaseConfig();
      print('🔧 حالة الإعدادات: ${getStatusIcon(configStatus)} ${getStatusMessage(configStatus)}');
      
      print('\n📋 تفاصيل الإعدادات:');
      print('   URL: ${SupabaseConfig.supabaseUrl}');
      final keyPreview = SupabaseConfig.supabaseAnonKey.length > 20
          ? SupabaseConfig.supabaseAnonKey.substring(0, 20) + '...'
          : SupabaseConfig.supabaseAnonKey;
      print('   Key: $keyPreview');
      
      if (configStatus == ConnectionStatus.configured) {
        print('\n🔗 اختبار الاتصال...');
        testConnection().then((connectionStatus) {
          print('   النتيجة: ${getStatusIcon(connectionStatus)} ${getStatusMessage(connectionStatus)}');
        });
      }
      
      print('\n💡 للإعداد السريع، راجع: SUPABASE_QUICK_SETUP.md');
      print('='*50 + '\n');
    }
  }

  /// فحص شامل مع تقرير
  static Future<Map<String, dynamic>> getFullReport() async {
    final configStatus = checkSupabaseConfig();
    ConnectionStatus? connectionStatus;
    
    if (configStatus == ConnectionStatus.configured) {
      connectionStatus = await testConnection();
    }

    return {
      'configStatus': configStatus,
      'connectionStatus': connectionStatus,
      'isReady': connectionStatus == ConnectionStatus.connected,
      'configMessage': getStatusMessage(configStatus),
      'connectionMessage': connectionStatus != null ? getStatusMessage(connectionStatus) : null,
      'recommendations': _getRecommendations(configStatus, connectionStatus),
    };
  }

  /// الحصول على توصيات بناءً على الحالة
  static List<String> _getRecommendations(ConnectionStatus configStatus, ConnectionStatus? connectionStatus) {
    final recommendations = <String>[];

    switch (configStatus) {
      case ConnectionStatus.notConfigured:
        recommendations.addAll([
          '1. اذهب إلى https://supabase.com وأنشئ مشروع جديد',
          '2. احصل على Project URL و Anon Key',
          '3. حدّث ملف lib/utils/supabase_config.dart',
          '4. شغّل ملفات SQL في Supabase Dashboard',
        ]);
        break;
      case ConnectionStatus.invalidUrl:
        recommendations.add('تأكد من أن URL يبدأ بـ https:// وينتهي بـ .supabase.co');
        break;
      case ConnectionStatus.invalidKey:
        recommendations.add('تأكد من أن Anon Key يبدأ بـ eyJ (JWT token)');
        break;
      case ConnectionStatus.configured:
        if (connectionStatus == ConnectionStatus.connectionFailed) {
          recommendations.addAll([
            'تحقق من اتصال الإنترنت',
            'تأكد من تشغيل ملفات SQL في Supabase',
            'تحقق من إعدادات المصادقة في Supabase Dashboard',
          ]);
        }
        break;
      case ConnectionStatus.connected:
        recommendations.add('🎉 كل شيء جاهز! يمكنك استخدام التطبيق بكامل وظائفه');
        break;
      case ConnectionStatus.connectionFailed:
      case ConnectionStatus.error:
        recommendations.add('راجع الأخطاء في وحدة التحكم وتأكد من الإعدادات');
        break;
    }

    return recommendations;
  }
}

/// حالات الاتصال المختلفة
enum ConnectionStatus {
  notConfigured,    // لم يتم الإعداد
  invalidUrl,       // URL غير صحيح
  invalidKey,       // Key غير صحيح
  configured,       // مُعدّ ولكن لم يتم اختبار الاتصال
  connected,        // متصل بنجاح
  connectionFailed, // فشل الاتصال
  error,           // خطأ عام
}
