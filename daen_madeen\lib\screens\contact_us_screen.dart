import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/device_activation_service.dart';
import '../utils/responsive_helper.dart';

/// Contact Us Screen matching the provided design
/// Features WhatsApp, Phone, Email, Share, and social media links
class ContactUsScreen extends StatefulWidget {
  final bool isBusinessOwner;

  const ContactUsScreen({
    super.key,
    this.isBusinessOwner = false,
  });

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String _activationCode = 'جاري التحميل...';
  String _deviceId = 'جاري التحميل...';
  bool _isActivated = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));

    // Load activation info immediately
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadActivationInfo();
    });
    _animationController.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload activation info when screen becomes active
    _loadActivationInfo();
  }

  Future<void> _loadActivationInfo() async {
    try {
      final activationService = DeviceActivationService.instance;

      // Get activation code
      final activationCode = await activationService.getDeviceActivationCode();
      final deviceId = await activationService.getDeviceId();
      final isActivated = await activationService.isDeviceActivated();

      if (mounted) {
        setState(() {
          _activationCode = activationCode.isNotEmpty ? activationCode : 'غير متوفر';
          _deviceId = deviceId.isNotEmpty ? deviceId : 'غير متوفر';
          _isActivated = isActivated;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _activationCode = 'خطأ في التحميل';
          _deviceId = 'خطأ في التحميل';
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Launch URL helper
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا يمكن فتح الرابط')),
        );
      }
    }
  }

  // Contact methods
  void _openWhatsApp() {
    _launchUrl('https://wa.me/967777747150');
  }

  void _makePhoneCall() {
    _launchUrl('tel:+967777747150');
  }

  void _sendEmail() {
    _launchUrl('mailto:<EMAIL>');
  }

  Future<void> _shareApp() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Get activation message
      final activationService = DeviceActivationService.instance;
      final message = await activationService.getActivationMessage();

      // Close loading dialog
      Navigator.of(context).pop();

      // Show activation code dialog instead of sharing
      _showActivationCodeDialog(message);

    } catch (e) {
      // Close loading dialog if still open
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء رمز التفعيل: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showActivationCodeDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: 400,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: const Color(0xFF3B82F6).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: const Icon(
                    Icons.qr_code,
                    size: 40,
                    color: Color(0xFF3B82F6),
                  ),
                ),

                const SizedBox(height: 20),

                // Title
                const Text(
                  'رمز تفعيل التطبيق',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1F2937),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // Message
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF9FAFB),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0xFFE5E7EB),
                    ),
                  ),
                  child: SelectableText(
                    message,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF374151),
                      height: 1.5,
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // Instructions
                const Text(
                  'انسخ النص أعلاه وأرسله للمطور لتفعيل حسابك',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF6B7280),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          // Copy to clipboard
                          await _copyToClipboard(message);
                          Navigator.of(context).pop();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF10B981),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: const Text(
                          'نسخ النص',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF6B7280),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          side: const BorderSide(
                            color: Color(0xFFD1D5DB),
                          ),
                        ),
                        child: const Text(
                          'إغلاق',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }





  Future<void> _copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم نسخ: ${text.length > 20 ? '${text.substring(0, 20)}...' : text}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في النسخ، يرجى المحاولة مرة أخرى'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _showAppInfo() {
    Navigator.pushNamed(context, '/subscription_plans');
  }

  // Build contact card widget matching the design
  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
            overflow: TextOverflow.visible,
          ),
        ),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: () => _copyToClipboard(value),
          child: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Icon(
              Icons.copy,
              size: 16,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color iconColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.85),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1E3A8A),
                    ),
                    textAlign: TextAlign.right,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 28,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build social media icon
  Widget _buildSocialIcon({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 65,
        height: 65,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.4),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 32,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.all(ResponsiveHelper.getResponsiveHorizontalPadding(context)),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  
                  // Header with back button and title
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const Expanded(
                          child: Text(
                            'تواصل معنا',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(width: 48), // Balance the back button
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // Activation Info Section (only for business owners)
                  if (!_isLoading && widget.isBusinessOwner)
                    SlideTransition(
                      position: _slideAnimation,
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 24),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: _isActivated ? const Color(0xFF10B981).withOpacity(0.1) : const Color(0xFFF59E0B).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: _isActivated ? const Color(0xFF10B981) : const Color(0xFFF59E0B),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Icon(
                                  _isActivated ? Icons.verified : Icons.pending,
                                  color: _isActivated ? const Color(0xFF10B981) : const Color(0xFFF59E0B),
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    _isActivated ? 'التطبيق مُفعّل' : 'في انتظار التفعيل',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: _isActivated ? const Color(0xFF10B981) : const Color(0xFFF59E0B),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            _buildInfoRow('رمز التفعيل', _activationCode),
                            const SizedBox(height: 8),
                            _buildInfoRow('معرف الجهاز', _deviceId),
                            if (!_isActivated) ...[
                              const SizedBox(height: 16),
                              const Text(
                                'أرسل رمز التفعيل للمطور لتفعيل حسابك',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF6B7280),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                  // Contact options
                  SlideTransition(
                    position: _slideAnimation,
                    child: Column(
                      children: [
                        // WhatsApp
                        _buildContactCard(
                          title: 'واتس اب',
                          subtitle: 'قم بمراسلتنا عبر الواتس اب',
                          icon: Icons.chat_bubble,
                          iconColor: const Color(0xFF25D366),
                          onTap: _openWhatsApp,
                        ),

                        // Phone
                        _buildContactCard(
                          title: 'إتصل بنا',
                          subtitle: '+967 777747150',
                          icon: Icons.phone,
                          iconColor: const Color(0xFF2196F3),
                          onTap: _makePhoneCall,
                        ),

                        // Email
                        _buildContactCard(
                          title: 'البريد الإلكتروني',
                          subtitle: '<EMAIL>',
                          icon: Icons.email,
                          iconColor: const Color(0xFF1976D2),
                          onTap: _sendEmail,
                        ),

                        // Share (only for business owners)
                        if (widget.isBusinessOwner)
                          _buildContactCard(
                            title: 'مشاركة رمز التفعيل',
                            subtitle: 'إضغط لمشاركة الرمز',
                            icon: Icons.share,
                            iconColor: const Color(0xFF2196F3),
                            onTap: _shareApp,
                          ),



                        // App activation (only for business owners)
                        if (widget.isBusinessOwner)
                          _buildContactCard(
                            title: 'تفعيل التطبيق',
                            subtitle: 'لعرض باقات الاشتراك والتفعيل',
                            icon: Icons.vpn_key,
                            iconColor: const Color(0xFF2196F3),
                            onTap: _showAppInfo,
                          ),

                        // Current version
                        _buildContactCard(
                          title: 'الإصدار الحالي',
                          subtitle: '0.0.1',
                          icon: Icons.verified_user,
                          iconColor: const Color(0xFF6366F1),
                          onTap: _showAppInfo,
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // Social media section
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                      children: [
                        const Text(
                          'تابعنا على مواقع التواصل الاجتماعي',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Social media icons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _buildSocialIcon(
                              icon: Icons.play_arrow,
                              color: const Color(0xFFFF0000),
                              onTap: () => _launchUrl('https://youtube.com'),
                            ),
                            
                            const SizedBox(width: 25),

                            _buildSocialIcon(
                              icon: Icons.camera_alt,
                              color: const Color(0xFFE4405F),
                              onTap: () => _launchUrl('https://instagram.com'),
                            ),

                            const SizedBox(width: 25),
                            
                            _buildSocialIcon(
                              icon: Icons.facebook,
                              color: const Color(0xFF1877F2),
                              onTap: () => _launchUrl('https://facebook.com'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
