// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveBusinessOwnerAdapter extends TypeAdapter<HiveBusinessOwner> {
  @override
  final int typeId = 0;

  @override
  HiveBusinessOwner read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveBusinessOwner(
      id: fields[0] as String,
      authUserId: fields[1] as String,
      businessName: fields[2] as String,
      ownerName: fields[3] as String,
      email: fields[4] as String,
      phone: fields[5] as String?,
      address: fields[6] as String?,
      createdAt: fields[7] as DateTime,
      updatedAt: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, HiveBusinessOwner obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.authUserId)
      ..writeByte(2)
      ..write(obj.businessName)
      ..writeByte(3)
      ..write(obj.ownerName)
      ..writeByte(4)
      ..write(obj.email)
      ..writeByte(5)
      ..write(obj.phone)
      ..writeByte(6)
      ..write(obj.address)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveBusinessOwnerAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HiveCustomerAdapter extends TypeAdapter<HiveCustomer> {
  @override
  final int typeId = 1;

  @override
  HiveCustomer read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveCustomer(
      id: fields[0] as String,
      businessOwnerId: fields[1] as String,
      authUserId: fields[2] as String,
      name: fields[3] as String,
      phone: fields[4] as String?,
      username: fields[5] as String,
      creditLimit: fields[6] as double,
      currentBalance: fields[7] as double,
      isActive: fields[8] as bool,
      createdAt: fields[9] as DateTime,
      updatedAt: fields[10] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, HiveCustomer obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessOwnerId)
      ..writeByte(2)
      ..write(obj.authUserId)
      ..writeByte(3)
      ..write(obj.name)
      ..writeByte(4)
      ..write(obj.phone)
      ..writeByte(5)
      ..write(obj.username)
      ..writeByte(6)
      ..write(obj.creditLimit)
      ..writeByte(7)
      ..write(obj.currentBalance)
      ..writeByte(8)
      ..write(obj.isActive)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveCustomerAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HiveDebtAdapter extends TypeAdapter<HiveDebt> {
  @override
  final int typeId = 2;

  @override
  HiveDebt read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveDebt(
      id: fields[0] as String,
      customerId: fields[1] as String,
      businessOwnerId: fields[2] as String,
      amount: fields[3] as double,
      description: fields[4] as String?,
      dateCreated: fields[5] as DateTime,
      isPaid: fields[6] as bool,
      createdAt: fields[7] as DateTime,
      updatedAt: fields[8] as DateTime,
      isSynced: fields[9] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, HiveDebt obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.customerId)
      ..writeByte(2)
      ..write(obj.businessOwnerId)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.dateCreated)
      ..writeByte(6)
      ..write(obj.isPaid)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt)
      ..writeByte(9)
      ..write(obj.isSynced);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveDebtAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HivePaymentAdapter extends TypeAdapter<HivePayment> {
  @override
  final int typeId = 3;

  @override
  HivePayment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HivePayment(
      id: fields[0] as String,
      debtId: fields[1] as String,
      customerId: fields[2] as String,
      businessOwnerId: fields[3] as String,
      amount: fields[4] as double,
      paymentDate: fields[5] as DateTime,
      status: fields[6] as String,
      notes: fields[7] as String?,
      createdAt: fields[8] as DateTime,
      updatedAt: fields[9] as DateTime,
      isSynced: fields[10] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, HivePayment obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.debtId)
      ..writeByte(2)
      ..write(obj.customerId)
      ..writeByte(3)
      ..write(obj.businessOwnerId)
      ..writeByte(4)
      ..write(obj.amount)
      ..writeByte(5)
      ..write(obj.paymentDate)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.notes)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.updatedAt)
      ..writeByte(10)
      ..write(obj.isSynced);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HivePaymentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HiveNotificationAdapter extends TypeAdapter<HiveNotification> {
  @override
  final int typeId = 4;

  @override
  HiveNotification read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveNotification(
      id: fields[0] as String,
      recipientId: fields[1] as String,
      senderId: fields[2] as String,
      recipientType: fields[3] as String,
      title: fields[4] as String,
      message: fields[5] as String,
      isRead: fields[6] as bool,
      relatedDebtId: fields[7] as String?,
      relatedPaymentId: fields[8] as String?,
      createdAt: fields[9] as DateTime,
      isSynced: fields[10] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, HiveNotification obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.recipientId)
      ..writeByte(2)
      ..write(obj.senderId)
      ..writeByte(3)
      ..write(obj.recipientType)
      ..writeByte(4)
      ..write(obj.title)
      ..writeByte(5)
      ..write(obj.message)
      ..writeByte(6)
      ..write(obj.isRead)
      ..writeByte(7)
      ..write(obj.relatedDebtId)
      ..writeByte(8)
      ..write(obj.relatedPaymentId)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.isSynced);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveNotificationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class HiveEmployeeAdapter extends TypeAdapter<HiveEmployee> {
  @override
  final int typeId = 5;

  @override
  HiveEmployee read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveEmployee(
      id: fields[0] as String,
      businessOwnerId: fields[1] as String,
      authUserId: fields[2] as String,
      name: fields[3] as String,
      phone: fields[4] as String?,
      email: fields[5] as String?,
      position: fields[6] as String,
      salary: fields[7] as double,
      address: fields[8] as String?,
      nationalId: fields[9] as String?,
      hireDate: fields[10] as DateTime,
      isActive: fields[11] as bool,
      notes: fields[12] as String?,
      createdAt: fields[13] as DateTime,
      updatedAt: fields[14] as DateTime,
      isSynced: fields[15] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, HiveEmployee obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessOwnerId)
      ..writeByte(2)
      ..write(obj.authUserId)
      ..writeByte(3)
      ..write(obj.name)
      ..writeByte(4)
      ..write(obj.phone)
      ..writeByte(5)
      ..write(obj.email)
      ..writeByte(6)
      ..write(obj.position)
      ..writeByte(7)
      ..write(obj.salary)
      ..writeByte(8)
      ..write(obj.address)
      ..writeByte(9)
      ..write(obj.nationalId)
      ..writeByte(10)
      ..write(obj.hireDate)
      ..writeByte(11)
      ..write(obj.isActive)
      ..writeByte(12)
      ..write(obj.notes)
      ..writeByte(13)
      ..write(obj.createdAt)
      ..writeByte(14)
      ..write(obj.updatedAt)
      ..writeByte(15)
      ..write(obj.isSynced);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveEmployeeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
