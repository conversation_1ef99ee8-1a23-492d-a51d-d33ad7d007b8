import 'package:uuid/uuid.dart';

/// User role enum for notifications
enum UserRole {
  businessOwner,
  customer;

  /// Convert from string (from database)
  static UserRole fromString(String role) {
    switch (role.toLowerCase()) {
      case 'business_owner':
        return UserRole.businessOwner;
      case 'customer':
        return UserRole.customer;
      default:
        return UserRole.customer;
    }
  }

  /// Convert to string (for database)
  String toDbString() {
    switch (this) {
      case UserRole.businessOwner:
        return 'business_owner';
      case UserRole.customer:
        return 'customer';
    }
  }

  /// Get display text in Arabic
  String get displayText {
    switch (this) {
      case UserRole.businessOwner:
        return 'صاحب العمل';
      case UserRole.customer:
        return 'عميل';
    }
  }
}

/// Notification model for دائن مدين (Creditor-Debtor) system
class AppNotification {
  final String id;
  final String recipientId;
  final String senderId;
  final UserRole recipientType;
  final String title;
  final String message;
  final bool isRead;
  final String? relatedDebtId;
  final String? relatedPaymentId;
  final DateTime createdAt;

  AppNotification({
    required this.id,
    required this.recipientId,
    required this.senderId,
    required this.recipientType,
    required this.title,
    required this.message,
    required this.isRead,
    this.relatedDebtId,
    this.relatedPaymentId,
    required this.createdAt,
  });

  /// Create AppNotification from JSON (from Supabase)
  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] as String,
      recipientId: json['recipient_id'] as String,
      senderId: json['sender_id'] as String,
      recipientType: UserRole.fromString(json['recipient_type'] as String),
      title: json['title'] as String,
      message: json['message'] as String,
      isRead: json['is_read'] as bool? ?? false,
      relatedDebtId: json['related_debt_id'] as String?,
      relatedPaymentId: json['related_payment_id'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// Convert AppNotification to JSON (for Supabase)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'recipient_id': recipientId,
      'sender_id': senderId,
      'recipient_type': recipientType.toDbString(),
      'title': title,
      'message': message,
      'is_read': isRead,
      'related_debt_id': relatedDebtId,
      'related_payment_id': relatedPaymentId,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Create a new AppNotification for insertion (without timestamps)
  Map<String, dynamic> toInsertJson() {
    return {
      'recipient_id': recipientId,
      'sender_id': senderId,
      'recipient_type': recipientType.toDbString(),
      'title': title,
      'message': message,
      'is_read': isRead,
      'related_debt_id': relatedDebtId,
      'related_payment_id': relatedPaymentId,
    };
  }

  /// Create a copy with updated fields
  AppNotification copyWith({
    String? id,
    String? recipientId,
    String? senderId,
    UserRole? recipientType,
    String? title,
    String? message,
    bool? isRead,
    String? relatedDebtId,
    String? relatedPaymentId,
    DateTime? createdAt,
  }) {
    return AppNotification(
      id: id ?? this.id,
      recipientId: recipientId ?? this.recipientId,
      senderId: senderId ?? this.senderId,
      recipientType: recipientType ?? this.recipientType,
      title: title ?? this.title,
      message: message ?? this.message,
      isRead: isRead ?? this.isRead,
      relatedDebtId: relatedDebtId ?? this.relatedDebtId,
      relatedPaymentId: relatedPaymentId ?? this.relatedPaymentId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Create a notification for new debt
  factory AppNotification.newDebt({
    required String recipientId,
    required String senderId,
    required UserRole recipientType,
    required String customerName,
    required double amount,
    required String debtId,
  }) {
    return AppNotification(
      id: const Uuid().v4(),
      recipientId: recipientId,
      senderId: senderId,
      recipientType: recipientType,
      title: 'دين جديد',
      message: 'تم إضافة دين جديد للعميل $customerName بمبلغ ${amount.toStringAsFixed(2)}',
      isRead: false,
      relatedDebtId: debtId,
      relatedPaymentId: null,
      createdAt: DateTime.now(),
    );
  }

  /// Create a notification for new payment
  factory AppNotification.newPayment({
    required String recipientId,
    required String senderId,
    required UserRole recipientType,
    required String customerName,
    required double amount,
    required String paymentId,
  }) {
    return AppNotification(
      id: const Uuid().v4(),
      recipientId: recipientId,
      senderId: senderId,
      recipientType: recipientType,
      title: 'دفعة جديدة',
      message: 'تم استلام دفعة من العميل $customerName بمبلغ ${amount.toStringAsFixed(2)}',
      isRead: false,
      relatedDebtId: null,
      relatedPaymentId: paymentId,
      createdAt: DateTime.now(),
    );
  }

  /// Create a payment reminder notification
  factory AppNotification.paymentReminder({
    required String recipientId,
    required String senderId,
    required double amount,
    required String debtId,
  }) {
    return AppNotification(
      id: const Uuid().v4(),
      recipientId: recipientId,
      senderId: senderId,
      recipientType: UserRole.customer,
      title: 'تذكير بالدفع',
      message: 'لديك دين مستحق بمبلغ ${amount.toStringAsFixed(2)}. يرجى السداد في أقرب وقت ممكن.',
      isRead: false,
      relatedDebtId: debtId,
      relatedPaymentId: null,
      createdAt: DateTime.now(),
    );
  }

  /// Mark notification as read
  AppNotification markAsRead() {
    return copyWith(isRead: true);
  }

  /// Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  @override
  String toString() {
    return 'AppNotification(id: $id, title: $title, isRead: $isRead, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
