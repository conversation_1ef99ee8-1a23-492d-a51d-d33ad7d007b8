import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:typed_data';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';

/// Complete professional debt registration screen
class AddDebtScreenComplete extends StatefulWidget {
  const AddDebtScreenComplete({super.key});

  @override
  State<AddDebtScreenComplete> createState() => _AddDebtScreenCompleteState();
}

class _AddDebtScreenCompleteState extends State<AddDebtScreenComplete> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _searchController = TextEditingController();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  String _selectedType = 'customer';
  String? _selectedPersonId;
  String _selectedPersonName = '';
  List<dynamic> _searchResults = [];
  bool _isSearching = false;
  bool _isLoading = false;
  // Removed attachedImages - not supported in current Debt model
  final ImagePicker _picker = ImagePicker();
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _searchController.addListener(_onSearchChanged);
  }
  
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    _searchController.dispose();
    super.dispose();
  }
  
  void _onSearchChanged() {
    if (_searchController.text.isEmpty) {
      setState(() {
        _searchResults.clear();
        _isSearching = false;
      });
      return;
    }
    
    setState(() {
      _isSearching = true;
    });
    
    _performSearch(_searchController.text);
  }
  
  Future<void> _performSearch(String query) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final businessOwner = authProvider.userProfile as BusinessOwner;
      
      List<dynamic> results = [];
      
      if (_selectedType == 'customer') {
        final customers = await CustomerService.getCustomersByBusinessOwner(businessOwner.id);
        results = customers.where((c) => 
          c.name.toLowerCase().contains(query.toLowerCase())
        ).toList();
      } else {
        final employees = await EmployeeService.getEmployeesByBusinessOwner(businessOwner.id);
        results = employees.where((e) => 
          e.name.toLowerCase().contains(query.toLowerCase())
        ).toList();
      }
      
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
      });
    }
  }
  
  // Image attachment functions removed - not supported in current model
  
  // Image options dialog removed - not supported in current model
  
  Future<void> _saveDebt() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedPersonId == null) {
      _showSnackBar('يرجى اختيار العميل أو الموظف', Colors.red);
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Only support customer debts for now (employees not supported in current model)
      if (_selectedType != 'customer' || _selectedPersonId == null) {
        _showSnackBar('يرجى اختيار عميل صحيح', Colors.red);
        return;
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final businessOwner = authProvider.userProfile as BusinessOwner;

      await DebtService.createDebt(
        customerId: _selectedPersonId!,
        businessOwnerId: businessOwner.id,
        amount: double.parse(_amountController.text),
        description: _descriptionController.text,
      );
      
      _showSuccessDialog();
      
    } catch (e) {
      _showSnackBar('خطأ في حفظ الدين: ${e.toString()}', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                color: Color(0xFF4CAF50),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'تم تسجيل الدين بنجاح',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 10),
            Text(
              'تم إضافة دين بقيمة ${_amountController.text} ريال',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('موافق'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _resetForm();
            },
            child: const Text('إضافة دين آخر'),
          ),
        ],
      ),
    );
  }
  
  void _resetForm() {
    _amountController.clear();
    _descriptionController.clear();
    _searchController.clear();
    setState(() {
      _selectedPersonId = null;
      _selectedPersonName = '';
      _searchResults.clear();
      // Images cleared - feature removed
    });
  }
  
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text(
          'مساعدة - تسجيل الديون',
          textDirection: TextDirection.rtl,
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '• اختر نوع الدين (عميل أو موظف)',
                textDirection: TextDirection.rtl,
              ),
              SizedBox(height: 8),
              Text(
                '• ابحث عن الشخص بالاسم',
                textDirection: TextDirection.rtl,
              ),
              SizedBox(height: 8),
              Text(
                '• أدخل مبلغ الدين',
                textDirection: TextDirection.rtl,
              ),
              SizedBox(height: 8),
              Text(
                '• اكتب تفاصيل الدين',
                textDirection: TextDirection.rtl,
              ),
              SizedBox(height: 8),
              Text(
                '• يمكنك إرفاق صور للأشياء المأخوذة',
                textDirection: TextDirection.rtl,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
