import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';
import '../../widgets/customer_card.dart';
import 'add_customer_screen.dart';
// Removed customer_details_screen.dart - not needed
import 'customers_list_screen.dart';
import 'remaining_amounts_screen.dart';
import 'delete_customer_screen.dart';
import 'create_usernames_screen.dart';

/// Customers management screen for business owners
class CustomersScreen extends StatefulWidget {
  const CustomersScreen({super.key});

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العملاء'),
        backgroundColor: const Color(0xFF0A0E27),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const SizedBox(height: 40),

                // Customer management card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Customer icon
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(
                          Icons.people,
                          size: 60,
                          color: Color(0xFF2D3561),
                        ),
                      ),

                      const SizedBox(height: 30),

                      // Management buttons
                      _buildManagementButton(
                        text: 'إضافة عميل جديد',
                        icon: Icons.person_add,
                        onTap: () => _navigateToAddCustomer(),
                      ),

                      const SizedBox(height: 12),

                      _buildManagementButton(
                        text: 'المبالغ المتبقية عند العملاء',
                        icon: Icons.account_balance_wallet,
                        onTap: () => _navigateToRemainingAmounts(),
                      ),

                      const SizedBox(height: 12),

                      _buildManagementButton(
                        text: 'عرض العملاء الحاليين',
                        icon: Icons.list,
                        onTap: () => _navigateToCustomersList(),
                      ),

                      const SizedBox(height: 12),

                      _buildManagementButton(
                        text: 'حذف عميل حالي',
                        icon: Icons.delete,
                        onTap: () => _navigateToDeleteCustomer(),
                      ),

                      const SizedBox(height: 12),

                      _buildManagementButton(
                        text: 'إنشاء اسم مستخدم للعملاء',
                        icon: Icons.account_circle,
                        onTap: () => _navigateToCreateUsernames(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildManagementButton({
    required String text,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        color: const Color(0xFF3B82F6),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              textDirection: TextDirection.rtl,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    text,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToAddCustomer() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddCustomerScreen()),
    );
  }

  void _navigateToCustomersList() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CustomersListScreen()),
    );
  }

  void _navigateToRemainingAmounts() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RemainingAmountsScreen()),
    );
  }

  void _navigateToDeleteCustomer() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const DeleteCustomerScreen()),
    );
  }

  void _navigateToCreateUsernames() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CreateUsernamesScreen()),
    );
  }
}
