import 'package:flutter/material.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: const Text(
          'سياسة الخصوصية',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: const Color(0xFF0A0E27),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF0A0E27),
                    Color(0xFF1A1F3A),
                    Color(0xFF2D3561),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(40),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.privacy_tip,
                      size: 40,
                      color: Color(0xFF0A0E27),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'سياسة الخصوصية',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'نحن نحترم خصوصيتك ونحمي بياناتك الشخصية',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Privacy Policy Content
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionTitle('1. جمع المعلومات'),
                  _buildSectionContent(
                    'نقوم بجمع المعلومات التي تقدمها لنا مباشرة عند استخدام تطبيق دائن مدين، مثل:\n'
                    '• معلومات الحساب (الاسم، البريد الإلكتروني)\n'
                    '• بيانات العملاء والديون\n'
                    '• معلومات الدفعات والمعاملات\n'
                    '• بيانات الاستخدام والتفاعل مع التطبيق'
                  ),

                  const SizedBox(height: 20),

                  _buildSectionTitle('2. استخدام المعلومات'),
                  _buildSectionContent(
                    'نستخدم المعلومات المجمعة للأغراض التالية:\n'
                    '• تقديم وتحسين خدمات التطبيق\n'
                    '• إدارة حسابك وبياناتك\n'
                    '• إرسال الإشعارات والتحديثات المهمة\n'
                    '• تحليل الاستخدام لتطوير الخدمة\n'
                    '• ضمان أمان وحماية البيانات'
                  ),

                  const SizedBox(height: 20),

                  _buildSectionTitle('3. حماية البيانات'),
                  _buildSectionContent(
                    'نتخذ إجراءات أمنية صارمة لحماية معلوماتك:\n'
                    '• تشفير البيانات أثناء النقل والتخزين\n'
                    '• استخدام خوادم آمنة ومحمية\n'
                    '• تطبيق أفضل الممارسات الأمنية\n'
                    '• مراقبة مستمرة للأنشطة المشبوهة\n'
                    '• نسخ احتياطية منتظمة للبيانات'
                  ),

                  const SizedBox(height: 20),

                  _buildSectionTitle('4. مشاركة المعلومات'),
                  _buildSectionContent(
                    'نحن لا نبيع أو نؤجر أو نشارك معلوماتك الشخصية مع أطراف ثالثة إلا في الحالات التالية:\n'
                    '• بموافقتك الصريحة\n'
                    '• لتقديم الخدمات المطلوبة\n'
                    '• للامتثال للقوانين واللوائح\n'
                    '• لحماية حقوقنا وحقوق المستخدمين'
                  ),

                  const SizedBox(height: 20),

                  _buildSectionTitle('5. حقوقك'),
                  _buildSectionContent(
                    'لديك الحق في:\n'
                    '• الوصول إلى بياناتك الشخصية\n'
                    '• تصحيح أو تحديث معلوماتك\n'
                    '• حذف حسابك وبياناتك\n'
                    '• تقييد معالجة بياناتك\n'
                    '• نقل بياناتك إلى خدمة أخرى\n'
                    '• الاعتراض على معالجة بياناتك'
                  ),

                  const SizedBox(height: 20),

                  _buildSectionTitle('6. الكوكيز وتقنيات التتبع'),
                  _buildSectionContent(
                    'نستخدم تقنيات مختلفة لتحسين تجربتك:\n'
                    '• ملفات تعريف الارتباط لحفظ تفضيلاتك\n'
                    '• تحليلات الاستخدام لتحسين الخدمة\n'
                    '• تقنيات الأمان لحماية حسابك\n'
                    'يمكنك التحكم في هذه الإعدادات من خلال التطبيق'
                  ),

                  const SizedBox(height: 20),

                  _buildSectionTitle('7. التحديثات'),
                  _buildSectionContent(
                    'قد نقوم بتحديث سياسة الخصوصية من وقت لآخر. سنقوم بإشعارك بأي تغييرات مهمة من خلال:\n'
                    '• إشعارات داخل التطبيق\n'
                    '• رسائل بريد إلكتروني\n'
                    '• تحديث تاريخ آخر تعديل\n'
                    'ننصحك بمراجعة هذه السياسة بانتظام'
                  ),

                  const SizedBox(height: 20),

                  _buildSectionTitle('8. التواصل معنا'),
                  _buildSectionContent(
                    'إذا كان لديك أي أسئلة حول سياسة الخصوصية، يمكنك التواصل معنا من خلال:\n'
                    '• صفحة "تواصل معنا" في التطبيق\n'
                    '• البريد الإلكتروني: <EMAIL>\n'
                    '• الهاتف: +966 XX XXX XXXX\n'
                    'سنرد على استفساراتك في أقرب وقت ممكن'
                  ),

                  const SizedBox(height: 24),

                  // Last Updated
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.update,
                          color: Colors.grey.shade600,
                          size: 24,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'آخر تحديث: ديسمبر 2024',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'الإصدار 1.0',
                          style: TextStyle(
                            color: Colors.grey.shade500,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Color(0xFF1F2937),
      ),
    );
  }

  Widget _buildSectionContent(String content) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Text(
        content,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade700,
          height: 1.6,
        ),
        textAlign: TextAlign.justify,
      ),
    );
  }
}
