import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';
import 'add_employee_screen.dart';
import 'employees_list_screen.dart';
import 'employee_details_screen.dart';
import 'delete_employee_screen.dart';
import 'create_employee_usernames_screen.dart';
import 'employee_statistics_screen.dart';

/// Employees management screen for business owners
class EmployeesScreen extends StatefulWidget {
  const EmployeesScreen({super.key});

  @override
  State<EmployeesScreen> createState() => _EmployeesScreenState();
}

class _EmployeesScreenState extends State<EmployeesScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الموظفين'),
        backgroundColor: const Color(0xFF0A0E27),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const SizedBox(height: 40),

                // Employee management card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Employee icon
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(
                          Icons.groups,
                          size: 60,
                          color: Color(0xFF2D3561),
                        ),
                      ),

                      const SizedBox(height: 30),

                      // Management buttons
                      _buildManagementButton(
                        text: 'إضافة موظف جديد',
                        icon: Icons.person_add,
                        onTap: () => _navigateToAddEmployee(),
                      ),

                      const SizedBox(height: 12),

                      _buildManagementButton(
                        text: 'عرض الموظفين الحاليين',
                        icon: Icons.list,
                        onTap: () => _navigateToEmployeesList(),
                      ),

                      const SizedBox(height: 12),

                      _buildManagementButton(
                        text: 'إنشاء أسماء مستخدمين للموظفين',
                        icon: Icons.account_circle,
                        onTap: () => _navigateToCreateEmployeeUsernames(),
                      ),

                      const SizedBox(height: 12),

                      _buildManagementButton(
                        text: 'إحصائيات الموظفين',
                        icon: Icons.analytics,
                        onTap: () => _navigateToEmployeeStats(),
                      ),

                      const SizedBox(height: 12),

                      _buildManagementButton(
                        text: 'حذف موظف',
                        icon: Icons.delete,
                        onTap: () => _navigateToDeleteEmployee(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildManagementButton({
    required String text,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        color: const Color(0xFF3B82F6),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              textDirection: TextDirection.rtl,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    text,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToAddEmployee() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddEmployeeScreen()),
    );
  }

  void _navigateToEmployeesList() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const EmployeesListScreen()),
    );
  }

  void _navigateToEmployeeStats() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const EmployeeStatisticsScreen()),
    );
  }

  void _navigateToDeleteEmployee() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const DeleteEmployeeScreen()),
    );
  }

  void _navigateToCreateEmployeeUsernames() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CreateEmployeeUsernamesScreen()),
    );
  }
}
