import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/supabase_config.dart';
import '../models/models.dart';
import '../services/local_storage_service.dart';
import '../services/connectivity_service.dart';

/// Notification service for دائن مدين (Creditor-Debtor) system
/// Handles notification CRUD operations with offline support and real-time updates
class NotificationService {
  static final SupabaseClient _client = SupabaseConfig.client;
  static final ConnectivityService _connectivity = ConnectivityService();

  /// Get all notifications for a recipient
  static Future<List<AppNotification>> getNotificationsByRecipient(String recipientId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.notifications
            .select()
            .eq('recipient_id', recipientId)
            .order('created_at', ascending: false);

        final notifications = response.map((json) => AppNotification.fromJson(json)).toList();

        // Save to local storage
        for (final notification in notifications) {
          await LocalStorageService.saveNotification(notification);
        }

        return notifications;
      } else {
        // Fetch from local storage
        final hiveNotifications = await LocalStorageService.getNotificationsByRecipient(recipientId);
        return hiveNotifications.map((hive) => AppNotification(
          id: hive.id,
          recipientId: hive.recipientId,
          senderId: hive.senderId,
          recipientType: UserRole.fromString(hive.recipientType),
          title: hive.title,
          message: hive.message,
          isRead: hive.isRead,
          relatedDebtId: hive.relatedDebtId,
          relatedPaymentId: hive.relatedPaymentId,
          createdAt: hive.createdAt,
        )).toList();
      }
    } catch (e) {
      // Fallback to local storage
      final hiveNotifications = await LocalStorageService.getNotificationsByRecipient(recipientId);
      return hiveNotifications.map((hive) => AppNotification(
        id: hive.id,
        recipientId: hive.recipientId,
        senderId: hive.senderId,
        recipientType: UserRole.fromString(hive.recipientType),
        title: hive.title,
        message: hive.message,
        isRead: hive.isRead,
        relatedDebtId: hive.relatedDebtId,
        relatedPaymentId: hive.relatedPaymentId,
        createdAt: hive.createdAt,
      )).toList();
    }
  }

  /// Get unread notification count
  static Future<int> getUnreadNotificationCount(String recipientId) async {
    try {
      if (_connectivity.isOnline) {
        final response = await _client.notifications
            .select('id')
            .eq('recipient_id', recipientId)
            .eq('is_read', false);

        return response.length;
      } else {
        return await LocalStorageService.getUnreadNotificationCount(recipientId);
      }
    } catch (e) {
      return await LocalStorageService.getUnreadNotificationCount(recipientId);
    }
  }

  /// Send notification for new debt
  static Future<AppNotification?> sendNewDebtNotification({
    required String recipientId,
    required String senderId,
    required UserRole recipientType,
    required String customerName,
    required double amount,
    required String debtId,
  }) async {
    try {
      final notification = AppNotification.newDebt(
        recipientId: recipientId,
        senderId: senderId,
        recipientType: recipientType,
        customerName: customerName,
        amount: amount,
        debtId: debtId,
      );

      return await _sendNotification(notification);
    } catch (e) {
      return null;
    }
  }

  /// Send notification for new payment
  static Future<AppNotification?> sendNewPaymentNotification({
    required String recipientId,
    required String senderId,
    required UserRole recipientType,
    required String customerName,
    required double amount,
    required String paymentId,
  }) async {
    try {
      final notification = AppNotification.newPayment(
        recipientId: recipientId,
        senderId: senderId,
        recipientType: recipientType,
        customerName: customerName,
        amount: amount,
        paymentId: paymentId,
      );

      return await _sendNotification(notification);
    } catch (e) {
      return null;
    }
  }

  /// Send payment reminder notification
  static Future<AppNotification?> sendPaymentReminderNotification({
    required String recipientId,
    required String senderId,
    required double amount,
    required String debtId,
  }) async {
    try {
      final notification = AppNotification.paymentReminder(
        recipientId: recipientId,
        senderId: senderId,
        amount: amount,
        debtId: debtId,
      );

      return await _sendNotification(notification);
    } catch (e) {
      return null;
    }
  }

  /// Send custom notification
  static Future<AppNotification?> sendCustomNotification({
    required String recipientId,
    required String senderId,
    required UserRole recipientType,
    required String title,
    required String message,
    String? relatedDebtId,
    String? relatedPaymentId,
  }) async {
    try {
      final notification = AppNotification(
        id: '',
        recipientId: recipientId,
        senderId: senderId,
        recipientType: recipientType,
        title: title,
        message: message,
        isRead: false,
        relatedDebtId: relatedDebtId,
        relatedPaymentId: relatedPaymentId,
        createdAt: DateTime.now(),
      );

      return await _sendNotification(notification);
    } catch (e) {
      return null;
    }
  }

  /// Internal method to send notification
  static Future<AppNotification?> _sendNotification(AppNotification notification) async {
    try {
      if (_connectivity.isOnline) {
        // Insert to Supabase
        final response = await _client.notifications
            .insert(notification.toInsertJson())
            .select()
            .single();

        final createdNotification = AppNotification.fromJson(response);
        await LocalStorageService.saveNotification(createdNotification);
        return createdNotification;
      } else {
        // Save to local storage only (will sync later)
        await LocalStorageService.saveNotification(notification, isSynced: false);
        return notification;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Mark notification as read
  static Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      if (_connectivity.isOnline) {
        // Update in Supabase
        await _client.notifications
            .update({'is_read': true})
            .eq('id', notificationId);
      }
      
      // Update in local storage
      await LocalStorageService.markNotificationAsRead(notificationId);
      return true;
    } catch (e) {
      // Try to update in local storage only
      await LocalStorageService.markNotificationAsRead(notificationId);
      return false;
    }
  }

  /// Mark all notifications as read for a recipient
  static Future<bool> markAllNotificationsAsRead(String recipientId) async {
    try {
      if (_connectivity.isOnline) {
        // Update in Supabase
        await _client.notifications
            .update({'is_read': true})
            .eq('recipient_id', recipientId)
            .eq('is_read', false);
      }
      
      // Update in local storage
      final notifications = await LocalStorageService.getNotificationsByRecipient(recipientId);
      for (final notification in notifications) {
        if (!notification.isRead) {
          await LocalStorageService.markNotificationAsRead(notification.id);
        }
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Delete notification
  static Future<bool> deleteNotification(String notificationId) async {
    try {
      if (_connectivity.isOnline) {
        // Delete from Supabase
        await _client.notifications.delete().eq('id', notificationId);
      }
      
      // Note: LocalStorageService doesn't have deleteNotification method
      // You would need to implement it if needed
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get all notifications for a business owner
  static Future<List<AppNotification>> getNotificationsByBusinessOwner(String businessOwnerId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.notifications
            .select()
            .eq('business_owner_id', businessOwnerId)
            .order('created_at', ascending: false);

        final notifications = response.map((json) => AppNotification.fromJson(json)).toList();

        // Save to local storage
        for (final notification in notifications) {
          await LocalStorageService.saveNotification(notification);
        }

        return notifications;
      } else {
        // Fetch from local storage
        final hiveNotifications = await LocalStorageService.getNotificationsByBusinessOwner(businessOwnerId);
        return hiveNotifications.map((hive) => AppNotification(
          id: hive.id,
          recipientId: hive.recipientId,
          senderId: hive.senderId,
          recipientType: UserRole.fromString(hive.recipientType),
          title: hive.title,
          message: hive.message,
          isRead: hive.isRead,
          relatedDebtId: hive.relatedDebtId,
          relatedPaymentId: hive.relatedPaymentId,
          createdAt: hive.createdAt,
        )).toList();
      }
    } catch (e) {
      // Fallback to local storage
      final hiveNotifications = await LocalStorageService.getNotificationsByBusinessOwner(businessOwnerId);
      return hiveNotifications.map((hive) => AppNotification(
        id: hive.id,
        recipientId: hive.recipientId,
        senderId: hive.senderId,
        recipientType: UserRole.fromString(hive.recipientType),
        title: hive.title,
        message: hive.message,
        isRead: hive.isRead,
        relatedDebtId: hive.relatedDebtId,
        relatedPaymentId: hive.relatedPaymentId,
        createdAt: hive.createdAt,
      )).toList();
    }
  }

  /// Create notification from AppNotification object
  static Future<AppNotification> createNotification(AppNotification notification) async {
    try {
      if (_connectivity.isOnline) {
        // Insert to Supabase
        final response = await _client.notifications
            .insert(notification.toInsertJson())
            .select()
            .single();

        final createdNotification = AppNotification.fromJson(response);
        await LocalStorageService.saveNotification(createdNotification);
        return createdNotification;
      } else {
        // Save to local storage only (will sync later)
        await LocalStorageService.saveNotification(notification, isSynced: false);
        return notification;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Get notification statistics
  static Future<Map<String, dynamic>> getNotificationStatistics(String recipientId) async {
    try {
      final notifications = await getNotificationsByRecipient(recipientId);

      final totalNotifications = notifications.length;
      final unreadNotifications = notifications.where((n) => !n.isRead).length;
      final readNotifications = notifications.where((n) => n.isRead).length;

      // Get notifications from last 7 days
      final weekAgo = DateTime.now().subtract(const Duration(days: 7));
      final recentNotifications = notifications.where((n) => n.createdAt.isAfter(weekAgo)).length;
      
      return {
        'totalNotifications': totalNotifications,
        'unreadNotifications': unreadNotifications,
        'readNotifications': readNotifications,
        'recentNotifications': recentNotifications,
        'readRate': totalNotifications > 0 ? (readNotifications / totalNotifications) * 100 : 0.0,
      };
    } catch (e) {
      return {
        'totalNotifications': 0,
        'unreadNotifications': 0,
        'readNotifications': 0,
        'recentNotifications': 0,
        'readRate': 0.0,
      };
    }
  }

  /// Subscribe to real-time notifications
  static RealtimeChannel subscribeToNotifications(String recipientId, Function(AppNotification) onNotification) {
    return _client
        .channel('notifications_$recipientId')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: 'notifications',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'recipient_id',
            value: recipientId,
          ),
          callback: (payload) {
            try {
              final notification = AppNotification.fromJson(payload.newRecord);
              // Save to local storage
              LocalStorageService.saveNotification(notification);
              onNotification(notification);
            } catch (e) {
              // Handle error
            }
          },
        )
        .subscribe();
  }

  /// Unsubscribe from real-time notifications
  static Future<void> unsubscribeFromNotifications(RealtimeChannel channel) async {
    await _client.removeChannel(channel);
  }

  /// Sync unsynced notifications when online
  static Future<void> syncUnsyncedNotifications() async {
    if (!_connectivity.isOnline) return;

    try {
      // Note: LocalStorageService doesn't have getUnsyncedNotifications method
      // You would need to implement it similar to debts and payments
      // For now, this is a placeholder
    } catch (e) {
      // Sync failed, will retry later
    }
  }
}
