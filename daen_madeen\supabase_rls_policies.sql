-- Row Level Security (RLS) Policies for دائن مدين (Creditor-Debtor) System
-- These policies ensure data security and proper access control

-- Enable RLS on all tables
ALTER TABLE business_owners ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Business Owners Policies
-- Business owners can only access their own data
CREATE POLICY "Business owners can view own data" ON business_owners
    FOR SELECT USING (auth_user_id = auth.uid());

CREATE POLICY "Business owners can update own data" ON business_owners
    FOR UPDATE USING (auth_user_id = auth.uid());

CREATE POLICY "Business owners can insert own data" ON business_owners
    FOR INSERT WITH CHECK (auth_user_id = auth.uid());

-- Customers Policies
-- Business owners can manage their customers
CREATE POLICY "Business owners can view their customers" ON customers
    FOR SELECT USING (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Business owners can insert customers" ON customers
    FOR INSERT WITH CHECK (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Business owners can update their customers" ON customers
    FOR UPDATE USING (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Business owners can delete their customers" ON customers
    FOR DELETE USING (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    );

-- Customers can view their own data
CREATE POLICY "Customers can view own data" ON customers
    FOR SELECT USING (auth_user_id = auth.uid());

-- Debts Policies
-- Business owners can manage debts for their customers
CREATE POLICY "Business owners can view debts for their customers" ON debts
    FOR SELECT USING (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Business owners can insert debts for their customers" ON debts
    FOR INSERT WITH CHECK (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
        AND customer_id IN (
            SELECT id FROM customers WHERE business_owner_id IN (
                SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Business owners can update debts for their customers" ON debts
    FOR UPDATE USING (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Business owners can delete debts for their customers" ON debts
    FOR DELETE USING (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    );

-- Customers can view their own debts
CREATE POLICY "Customers can view own debts" ON debts
    FOR SELECT USING (
        customer_id IN (
            SELECT id FROM customers WHERE auth_user_id = auth.uid()
        )
    );

-- Payments Policies
-- Business owners can manage payments for their customers
CREATE POLICY "Business owners can view payments for their customers" ON payments
    FOR SELECT USING (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Business owners can insert payments for their customers" ON payments
    FOR INSERT WITH CHECK (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
        AND customer_id IN (
            SELECT id FROM customers WHERE business_owner_id IN (
                SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Business owners can update payments for their customers" ON payments
    FOR UPDATE USING (
        business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    );

-- Customers can view their own payments
CREATE POLICY "Customers can view own payments" ON payments
    FOR SELECT USING (
        customer_id IN (
            SELECT id FROM customers WHERE auth_user_id = auth.uid()
        )
    );

-- Notifications Policies
-- Users can view their own notifications
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (recipient_id = auth.uid());

CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (recipient_id = auth.uid());

-- Business owners can send notifications to their customers
CREATE POLICY "Business owners can send notifications to customers" ON notifications
    FOR INSERT WITH CHECK (
        sender_id = auth.uid()
        AND (
            (recipient_type = 'customer' AND recipient_id IN (
                SELECT auth_user_id FROM customers WHERE business_owner_id IN (
                    SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
                )
            ))
            OR
            (recipient_type = 'business_owner' AND recipient_id = auth.uid())
        )
    );

-- Customers can send notifications to their business owner
CREATE POLICY "Customers can send notifications to business owner" ON notifications
    FOR INSERT WITH CHECK (
        sender_id = auth.uid()
        AND recipient_type = 'business_owner'
        AND recipient_id IN (
            SELECT bo.auth_user_id 
            FROM business_owners bo
            JOIN customers c ON c.business_owner_id = bo.id
            WHERE c.auth_user_id = auth.uid()
        )
    );
