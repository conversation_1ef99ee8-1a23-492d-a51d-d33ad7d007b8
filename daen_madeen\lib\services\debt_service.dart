import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/supabase_config.dart';
import '../models/models.dart';
import '../services/local_storage_service.dart';
import '../services/connectivity_service.dart';

/// Debt service for دائن مدين (Creditor-Debtor) system
/// Handles debt CRUD operations with offline support
class DebtService {
  static final SupabaseClient _client = SupabaseConfig.client;
  static final ConnectivityService _connectivity = ConnectivityService();

  /// Get all debts for a customer
  static Future<List<Debt>> getDebtsByCustomer(String customerId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.debts
            .select()
            .eq('customer_id', customerId)
            .order('date_created', ascending: false);

        final debts = response.map((json) => Debt.fromJson(json)).toList();

        // Save to local storage
        for (final debt in debts) {
          await LocalStorageService.saveDebt(debt);
        }

        return debts;
      } else {
        // Fetch from local storage
        final hiveDebts = await LocalStorageService.getDebtsByCustomer(customerId);
        return hiveDebts.map((hive) => Debt(
          id: hive.id,
          customerId: hive.customerId,
          businessOwnerId: hive.businessOwnerId,
          amount: hive.amount,
          description: hive.description,
          dateCreated: hive.dateCreated,
          isPaid: hive.isPaid,
          createdAt: hive.createdAt,
          updatedAt: hive.updatedAt,
        )).toList();
      }
    } catch (e) {
      // Fallback to local storage
      final hiveDebts = await LocalStorageService.getDebtsByCustomer(customerId);
      return hiveDebts.map((hive) => Debt(
        id: hive.id,
        customerId: hive.customerId,
        businessOwnerId: hive.businessOwnerId,
        amount: hive.amount,
        description: hive.description,
        dateCreated: hive.dateCreated,
        isPaid: hive.isPaid,
        createdAt: hive.createdAt,
        updatedAt: hive.updatedAt,
      )).toList();
    }
  }

  /// Get all debts for a business owner
  static Future<List<Debt>> getDebtsByBusinessOwner(String businessOwnerId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.debts
            .select()
            .eq('business_owner_id', businessOwnerId)
            .order('date_created', ascending: false);

        final debts = response.map((json) => Debt.fromJson(json)).toList();

        // Save to local storage
        for (final debt in debts) {
          await LocalStorageService.saveDebt(debt);
        }

        return debts;
      } else {
        // Fetch from local storage
        final hiveDebts = await LocalStorageService.getDebtsByBusinessOwner(businessOwnerId);
        return hiveDebts.map((hive) => Debt(
          id: hive.id,
          customerId: hive.customerId,
          businessOwnerId: hive.businessOwnerId,
          amount: hive.amount,
          description: hive.description,
          dateCreated: hive.dateCreated,
          isPaid: hive.isPaid,
          createdAt: hive.createdAt,
          updatedAt: hive.updatedAt,
        )).toList();
      }
    } catch (e) {
      // Fallback to local storage
      final hiveDebts = await LocalStorageService.getDebtsByBusinessOwner(businessOwnerId);
      return hiveDebts.map((hive) => Debt(
        id: hive.id,
        customerId: hive.customerId,
        businessOwnerId: hive.businessOwnerId,
        amount: hive.amount,
        description: hive.description,
        dateCreated: hive.dateCreated,
        isPaid: hive.isPaid,
        createdAt: hive.createdAt,
        updatedAt: hive.updatedAt,
      )).toList();
    }
  }

  /// Get debt by ID
  static Future<Debt?> getDebtById(String debtId) async {
    try {
      if (_connectivity.isOnline) {
        // Fetch from Supabase
        final response = await _client.debts
            .select()
            .eq('id', debtId)
            .maybeSingle();

        if (response != null) {
          final debt = Debt.fromJson(response);
          await LocalStorageService.saveDebt(debt);
          return debt;
        }
      } else {
        // Fetch from local storage
        final hiveDebt = await LocalStorageService.getDebt(debtId);
        if (hiveDebt != null) {
          return Debt(
            id: hiveDebt.id,
            customerId: hiveDebt.customerId,
            businessOwnerId: hiveDebt.businessOwnerId,
            amount: hiveDebt.amount,
            description: hiveDebt.description,
            dateCreated: hiveDebt.dateCreated,
            isPaid: hiveDebt.isPaid,
            createdAt: hiveDebt.createdAt,
            updatedAt: hiveDebt.updatedAt,
          );
        }
      }
      return null;
    } catch (e) {
      // Fallback to local storage
      final hiveDebt = await LocalStorageService.getDebt(debtId);
      if (hiveDebt != null) {
        return Debt(
          id: hiveDebt.id,
          customerId: hiveDebt.customerId,
          businessOwnerId: hiveDebt.businessOwnerId,
          amount: hiveDebt.amount,
          description: hiveDebt.description,
          dateCreated: hiveDebt.dateCreated,
          isPaid: hiveDebt.isPaid,
          createdAt: hiveDebt.createdAt,
          updatedAt: hiveDebt.updatedAt,
        );
      }
      return null;
    }
  }

  /// Create a new debt
  static Future<Debt?> createDebt({
    required String customerId,
    required String businessOwnerId,
    required double amount,
    String? description,
    DateTime? dateCreated,
  }) async {
    try {
      if (amount <= 0) {
        throw Exception('مبلغ الدين يجب أن يكون أكبر من صفر');
      }

      final debt = Debt.create(
        customerId: customerId,
        businessOwnerId: businessOwnerId,
        amount: amount,
        description: description,
        dateCreated: dateCreated,
      );

      if (_connectivity.isOnline) {
        // Insert to Supabase
        final response = await _client.debts
            .insert(debt.toInsertJson())
            .select()
            .single();

        final createdDebt = Debt.fromJson(response);
        await LocalStorageService.saveDebt(createdDebt);
        return createdDebt;
      } else {
        // Save to local storage only (will sync later)
        await LocalStorageService.saveDebt(debt, isSynced: false);
        return debt;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Update debt
  static Future<Debt?> updateDebt(Debt debt) async {
    try {
      if (_connectivity.isOnline) {
        // Update in Supabase
        final response = await _client.debts
            .update(debt.toJson())
            .eq('id', debt.id)
            .select()
            .single();

        final updatedDebt = Debt.fromJson(response);
        await LocalStorageService.saveDebt(updatedDebt);
        return updatedDebt;
      } else {
        // Update in local storage only (will sync later)
        await LocalStorageService.saveDebt(debt, isSynced: false);
        return debt;
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Mark debt as paid
  static Future<Debt?> markDebtAsPaid(String debtId) async {
    try {
      final debt = await getDebtById(debtId);
      if (debt != null && !debt.isPaid) {
        final updatedDebt = debt.markAsPaid();
        return await updateDebt(updatedDebt);
      }
      return debt;
    } catch (e) {
      return null;
    }
  }

  /// Mark debt as unpaid
  static Future<Debt?> markDebtAsUnpaid(String debtId) async {
    try {
      final debt = await getDebtById(debtId);
      if (debt != null && debt.isPaid) {
        final updatedDebt = debt.markAsUnpaid();
        return await updateDebt(updatedDebt);
      }
      return debt;
    } catch (e) {
      return null;
    }
  }

  /// Delete debt
  static Future<bool> deleteDebt(String debtId) async {
    try {
      if (_connectivity.isOnline) {
        // Delete from Supabase
        await _client.debts.delete().eq('id', debtId);
      }
      
      // Delete from local storage
      await LocalStorageService.deleteDebt(debtId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get unpaid debts for a customer
  static Future<List<Debt>> getUnpaidDebtsByCustomer(String customerId) async {
    try {
      final debts = await getDebtsByCustomer(customerId);
      return debts.where((debt) => !debt.isPaid).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get overdue debts (older than 30 days and unpaid)
  static Future<List<Debt>> getOverdueDebts(String businessOwnerId) async {
    try {
      final debts = await getDebtsByBusinessOwner(businessOwnerId);
      return debts.where((debt) => debt.isOverdue).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get debt statistics for a business owner
  static Future<Map<String, dynamic>> getDebtStatistics(String businessOwnerId) async {
    try {
      final debts = await getDebtsByBusinessOwner(businessOwnerId);
      
      final totalDebts = debts.length;
      final paidDebts = debts.where((d) => d.isPaid).length;
      final unpaidDebts = debts.where((d) => !d.isPaid).length;
      final overdueDebts = debts.where((d) => d.isOverdue).length;
      
      final totalAmount = debts.fold<double>(0, (sum, d) => sum + d.amount);
      final paidAmount = debts.where((d) => d.isPaid).fold<double>(0, (sum, d) => sum + d.amount);
      final unpaidAmount = debts.where((d) => !d.isPaid).fold<double>(0, (sum, d) => sum + d.amount);
      
      return {
        'totalDebts': totalDebts,
        'paidDebts': paidDebts,
        'unpaidDebts': unpaidDebts,
        'overdueDebts': overdueDebts,
        'totalAmount': totalAmount,
        'paidAmount': paidAmount,
        'unpaidAmount': unpaidAmount,
        'paymentRate': totalDebts > 0 ? (paidDebts / totalDebts) * 100 : 0.0,
        'averageDebtAmount': totalDebts > 0 ? totalAmount / totalDebts : 0.0,
      };
    } catch (e) {
      return {
        'totalDebts': 0,
        'paidDebts': 0,
        'unpaidDebts': 0,
        'overdueDebts': 0,
        'totalAmount': 0.0,
        'paidAmount': 0.0,
        'unpaidAmount': 0.0,
        'paymentRate': 0.0,
        'averageDebtAmount': 0.0,
      };
    }
  }

  /// Get recent debts (last 30 days)
  static Future<List<Debt>> getRecentDebts(String businessOwnerId, {int days = 30}) async {
    try {
      final debts = await getDebtsByBusinessOwner(businessOwnerId);
      final cutoffDate = DateTime.now().subtract(Duration(days: days));
      
      return debts.where((debt) => debt.dateCreated.isAfter(cutoffDate)).toList();
    } catch (e) {
      return [];
    }
  }

  /// Sync unsynced debts when online
  static Future<void> syncUnsyncedDebts() async {
    if (!_connectivity.isOnline) return;

    try {
      final unsyncedDebts = await LocalStorageService.getUnsyncedDebts();
      
      for (final hiveDebt in unsyncedDebts) {
        try {
          final debt = Debt(
            id: hiveDebt.id,
            customerId: hiveDebt.customerId,
            businessOwnerId: hiveDebt.businessOwnerId,
            amount: hiveDebt.amount,
            description: hiveDebt.description,
            dateCreated: hiveDebt.dateCreated,
            isPaid: hiveDebt.isPaid,
            createdAt: hiveDebt.createdAt,
            updatedAt: hiveDebt.updatedAt,
          );

          // Try to sync to Supabase
          await _client.debts.upsert(debt.toJson());
          
          // Mark as synced
          await LocalStorageService.markDebtAsSynced(debt.id);
        } catch (e) {
          // Continue with next debt if one fails
          continue;
        }
      }
    } catch (e) {
      // Sync failed, will retry later
    }
  }
}
