import 'package:flutter/material.dart';
import 'dart:async';

/// Advertisement banner widget with auto-scrolling images
class AdvertisementBanner extends StatefulWidget {
  final List<AdvertisementItem> advertisements;
  final double height;
  final Duration autoScrollDuration;
  final Duration transitionDuration;
  final bool enableAutoScroll;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;

  const AdvertisementBanner({
    super.key,
    required this.advertisements,
    this.height = 120,
    this.autoScrollDuration = const Duration(seconds: 4),
    this.transitionDuration = const Duration(milliseconds: 800),
    this.enableAutoScroll = true,
    this.margin,
    this.borderRadius,
  });

  @override
  State<AdvertisementBanner> createState() => _AdvertisementBannerState();
}

class _AdvertisementBannerState extends State<AdvertisementBanner>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late Timer? _autoScrollTimer;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _fadeController = AnimationController(
      duration: widget.transitionDuration,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    if (widget.enableAutoScroll && widget.advertisements.isNotEmpty) {
      _startAutoScroll();
    }
    _fadeController.forward();
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _pageController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    _autoScrollTimer = Timer.periodic(widget.autoScrollDuration, (timer) {
      if (widget.advertisements.isNotEmpty) {
        final nextIndex = (_currentIndex + 1) % widget.advertisements.length;
        _pageController.animateToPage(
          nextIndex,
          duration: widget.transitionDuration,
          curve: Curves.easeInOutCubic,
        );
      }
    });
  }

  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.advertisements.isEmpty) {
      return const SizedBox.shrink();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        height: widget.height,
        margin: widget.margin ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
          child: Stack(
            children: [
              // Main PageView
              GestureDetector(
                onTapDown: (_) => _stopAutoScroll(),
                onTapUp: (_) => widget.enableAutoScroll ? _startAutoScroll() : null,
                onTapCancel: () => widget.enableAutoScroll ? _startAutoScroll() : null,
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: _onPageChanged,
                  itemCount: widget.advertisements.length,
                  itemBuilder: (context, index) {
                    final ad = widget.advertisements[index];
                    return _buildAdvertisementItem(ad);
                  },
                ),
              ),

              // Page indicators
              if (widget.advertisements.length > 1)
                Positioned(
                  bottom: 12,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      widget.advertisements.length,
                      (index) => _buildPageIndicator(index),
                    ),
                  ),
                ),

              // Gradient overlay for better text readability
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                height: 60,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.6),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdvertisementItem(AdvertisementItem ad) {
    return Container(
      decoration: BoxDecoration(
        gradient: ad.backgroundGradient ?? _getDefaultGradient(),
      ),
      child: Stack(
        children: [
          // Background image
          if (ad.imageUrl != null)
            Positioned.fill(
              child: Image.network(
                ad.imageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    decoration: BoxDecoration(
                      gradient: _getDefaultGradient(),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.image_not_supported,
                        color: Colors.white54,
                        size: 40,
                      ),
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    decoration: BoxDecoration(
                      gradient: _getDefaultGradient(),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    ),
                  );
                },
              ),
            ),

          // Content overlay
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (ad.title != null)
                  Text(
                    ad.title!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          offset: Offset(1, 1),
                          blurRadius: 3,
                          color: Colors.black54,
                        ),
                      ],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                if (ad.subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    ad.subtitle!,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      shadows: [
                        Shadow(
                          offset: Offset(1, 1),
                          blurRadius: 3,
                          color: Colors.black54,
                        ),
                      ],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(int index) {
    final isActive = index == _currentIndex;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      width: isActive ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: isActive ? Colors.white : Colors.white54,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }

  LinearGradient _getDefaultGradient() {
    return const LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Color(0xFF667eea),
        Color(0xFF764ba2),
      ],
    );
  }
}

/// Advertisement item model
class AdvertisementItem {
  final String? imageUrl;
  final String? title;
  final String? subtitle;
  final LinearGradient? backgroundGradient;
  final VoidCallback? onTap;

  const AdvertisementItem({
    this.imageUrl,
    this.title,
    this.subtitle,
    this.backgroundGradient,
    this.onTap,
  });
}
