import 'package:uuid/uuid.dart';

/// Debt model for دائن مدين (Creditor-Debtor) system
class Debt {
  final String id;
  final String customerId;
  final String businessOwnerId;
  final double amount;
  final String? description;
  final DateTime dateCreated;
  final bool isPaid;
  final double paidAmount;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// Calculate remaining amount to be paid
  double get remainingAmount => amount - paidAmount;

  Debt({
    required this.id,
    required this.customerId,
    required this.businessOwnerId,
    required this.amount,
    this.description,
    required this.dateCreated,
    required this.isPaid,
    this.paidAmount = 0.0,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Debt from JSON (from Supabase)
  factory Debt.fromJson(Map<String, dynamic> json) {
    return Debt(
      id: json['id'] as String,
      customerId: json['customer_id'] as String,
      businessOwnerId: json['business_owner_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String?,
      dateCreated: DateTime.parse(json['date_created'] as String),
      isPaid: json['is_paid'] as bool? ?? false,
      paidAmount: (json['paid_amount'] as num?)?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert Debt to JSON (for Supabase)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'business_owner_id': businessOwnerId,
      'amount': amount,
      'description': description,
      'date_created': dateCreated.toIso8601String(),
      'is_paid': isPaid,
      'paid_amount': paidAmount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a new Debt for insertion (without timestamps)
  Map<String, dynamic> toInsertJson() {
    return {
      'customer_id': customerId,
      'business_owner_id': businessOwnerId,
      'amount': amount,
      'description': description,
      'date_created': dateCreated.toIso8601String(),
      'is_paid': isPaid,
      'paid_amount': paidAmount,
    };
  }

  /// Create a copy with updated fields
  Debt copyWith({
    String? id,
    String? customerId,
    String? businessOwnerId,
    double? amount,
    String? description,
    DateTime? dateCreated,
    bool? isPaid,
    double? paidAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Debt(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      businessOwnerId: businessOwnerId ?? this.businessOwnerId,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      dateCreated: dateCreated ?? this.dateCreated,
      isPaid: isPaid ?? this.isPaid,
      paidAmount: paidAmount ?? this.paidAmount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create a Debt for new entry
  factory Debt.create({
    required String customerId,
    required String businessOwnerId,
    required double amount,
    String? description,
    DateTime? dateCreated,
  }) {
    final now = DateTime.now();
    return Debt(
      id: const Uuid().v4(),
      customerId: customerId,
      businessOwnerId: businessOwnerId,
      amount: amount,
      description: description,
      dateCreated: dateCreated ?? now,
      isPaid: false,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Mark debt as paid
  Debt markAsPaid() {
    return copyWith(
      isPaid: true,
      updatedAt: DateTime.now(),
    );
  }

  /// Mark debt as unpaid
  Debt markAsUnpaid() {
    return copyWith(
      isPaid: false,
      updatedAt: DateTime.now(),
    );
  }

  /// Get formatted amount string
  String get formattedAmount {
    return amount.toStringAsFixed(2);
  }

  /// Get debt status text
  String get statusText {
    return isPaid ? 'مدفوع' : 'غير مدفوع'; // Paid / Unpaid in Arabic
  }

  /// Get debt age in days
  int get ageInDays {
    return DateTime.now().difference(dateCreated).inDays;
  }

  /// Check if debt is overdue (more than 30 days)
  bool get isOverdue {
    return !isPaid && ageInDays > 30;
  }

  @override
  String toString() {
    return 'Debt(id: $id, amount: $amount, isPaid: $isPaid, dateCreated: $dateCreated)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Debt && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
