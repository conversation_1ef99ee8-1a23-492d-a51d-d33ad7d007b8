import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/services.dart';
import 'customer_report_screen.dart';
import 'employee_report_screen.dart';
import 'financial_summary_screen.dart';

/// Reports main screen for business owners
class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        backgroundColor: const Color(0xFF0A0E27),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const SizedBox(height: 10),

                // Reports header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Reports icon
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(
                          Icons.assessment,
                          size: 40,
                          color: Color(0xFF2D3561),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Report type buttons
                      _buildReportButton(
                        text: 'تقارير العملاء',
                        subtitle: 'تقارير مفصلة عن ديون ومدفوعات العملاء',
                        icon: Icons.people,
                        color: const Color(0xFF3498DB),
                        onTap: () => _navigateToCustomerReports(),
                      ),

                      const SizedBox(height: 12),

                      _buildReportButton(
                        text: 'تقارير الموظفين',
                        subtitle: 'تقارير شاملة عن الموظفين ورواتبهم',
                        icon: Icons.groups,
                        color: const Color(0xFF9B59B6),
                        onTap: () => _navigateToEmployeeReports(),
                      ),

                      const SizedBox(height: 12),

                      _buildReportButton(
                        text: 'التقرير المالي الإجمالي',
                        subtitle: 'ملخص شامل للوضع المالي للمؤسسة',
                        icon: Icons.analytics,
                        color: const Color(0xFF27AE60),
                        onTap: () => _navigateToFinancialSummary(),
                      ),

                      const SizedBox(height: 12),

                      _buildReportButton(
                        text: 'تقارير مخصصة',
                        subtitle: 'إنشاء تقارير حسب الحاجة والفترة المحددة',
                        icon: Icons.tune,
                        color: const Color(0xFFE67E22),
                        onTap: () => _navigateToCustomReports(),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Quick stats
                _buildQuickStats(),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReportButton({
    required String text,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              textDirection: TextDirection.rtl,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        text,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 11,
                        ),
                        textDirection: TextDirection.rtl,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withOpacity(0.7),
                  size: 14,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات سريعة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildQuickStatItem(
                  'التقارير المُنشأة',
                  '0',
                  Icons.description,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickStatItem(
                  'آخر تقرير',
                  'لا يوجد',
                  Icons.schedule,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: Colors.white.withOpacity(0.8),
            size: 20,
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  void _navigateToCustomerReports() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CustomerReportScreen()),
    );
  }

  void _navigateToEmployeeReports() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const EmployeeReportScreen()),
    );
  }

  void _navigateToFinancialSummary() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const FinancialSummaryScreen()),
    );
  }

  void _navigateToCustomReports() {
    // TODO: Implement custom reports screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('التقارير المخصصة ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
