/// خدمة مصادقة مبسطة للوضع التجريبي
/// Simple Authentication service for demo mode
class SimpleAuthResponse {
  final bool success;
  final String? error;
  final dynamic user;

  SimpleAuthResponse({
    required this.success,
    this.error,
    this.user,
  });
}

class SimpleAuthService {
  /// Business Owner Login (Demo)
  static Future<SimpleAuthResponse> loginBusinessOwner({
    required String email,
    required String password,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Demo credentials - Main account
    if (email == '<EMAIL>' && password == '123456') {
      return SimpleAuthResponse(
        success: true,
        user: {'email': email, 'type': 'business_owner'},
      );
    }

    // Demo credentials - Test account
    if (email == '<EMAIL>' && password == '123456') {
      return SimpleAuthResponse(
        success: true,
        user: {'email': email, 'type': 'business_owner'},
      );
    }

    return SimpleAuthResponse(
      success: false,
      error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    );
  }

  /// Customer Login (Demo)
  static Future<SimpleAuthResponse> loginCustomer({
    required String username,
    required String password,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Demo credentials - Main customer account
    if (username == 'othman' && password == '123456') {
      return SimpleAuthResponse(
        success: true,
        user: {'username': username, 'type': 'customer'},
      );
    }

    // Demo credentials - Test customer account
    if (username == 'sara' && password == '123456') {
      return SimpleAuthResponse(
        success: true,
        user: {'username': username, 'type': 'customer'},
      );
    }

    return SimpleAuthResponse(
      success: false,
      error: 'اسم المستخدم أو كلمة المرور غير صحيحة',
    );
  }

  /// Register Business Owner (Demo)
  static Future<SimpleAuthResponse> registerBusinessOwner({
    required String email,
    required String password,
    required String businessName,
    required String ownerName,
    String? phone,
    String? address,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 2));
    
    return SimpleAuthResponse(
      success: true,
      user: {
        'email': email,
        'type': 'business_owner',
        'businessName': businessName,
        'ownerName': ownerName,
      },
    );
  }

  /// Reset Password (Demo)
  static Future<SimpleAuthResponse> resetPassword(String email) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    
    return SimpleAuthResponse(success: true);
  }

  /// Sign Out (Demo)
  static Future<void> signOut() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
  }
}
