import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

/// Service for managing device activation codes
class DeviceActivationService {
  static const String _deviceIdKey = 'device_activation_id';
  static const String _activationCodeKey = 'device_activation_code';
  static const String _isActivatedKey = 'device_is_activated';
  
  static DeviceActivationService? _instance;
  static DeviceActivationService get instance {
    _instance ??= DeviceActivationService._();
    return _instance!;
  }
  
  DeviceActivationService._();
  
  /// Generate or retrieve existing device activation code
  Future<String> getDeviceActivationCode() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Check if activation code already exists
    String? existingCode = prefs.getString(_activationCodeKey);
    if (existingCode != null && existingCode.isNotEmpty) {
      return existingCode;
    }
    
    // Generate new activation code
    final activationCode = _generateActivationCode();
    
    // Save the activation code
    await prefs.setString(_activationCodeKey, activationCode);
    
    return activationCode;
  }
  
  /// Generate unique device ID
  Future<String> getDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Check if device ID already exists
    String? existingId = prefs.getString(_deviceIdKey);
    if (existingId != null && existingId.isNotEmpty) {
      return existingId;
    }
    
    // Generate new device ID
    const uuid = Uuid();
    final deviceId = uuid.v4();
    
    // Save the device ID
    await prefs.setString(_deviceIdKey, deviceId);
    
    return deviceId;
  }
  
  /// Generate activation code based on device ID and timestamp
  String _generateActivationCode() {
    // Create a unique code using timestamp and random elements
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random();
    
    // Generate 6-digit activation code
    final code1 = (timestamp % 1000).toString().padLeft(3, '0');
    final code2 = random.nextInt(1000).toString().padLeft(3, '0');
    
    return '$code1$code2';
  }
  
  /// Get formatted activation message for sharing
  Future<String> getActivationMessage() async {
    final activationCode = await getDeviceActivationCode();
    final deviceId = await getDeviceId();
    
    return '''
🔐 طلب تفعيل تطبيق دائن مدين

📱 رمز التفعيل: $activationCode
🆔 معرف الجهاز: ${deviceId.substring(0, 8)}...

يرجى تفعيل حسابي في التطبيق

📧 <EMAIL>
📞 +967 777747150
''';
  }
  
  /// Check if device is activated
  Future<bool> isDeviceActivated() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isActivatedKey) ?? false;
  }
  
  /// Activate device (called when activation is confirmed)
  Future<void> activateDevice() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isActivatedKey, true);

    // Log activation for debugging
    print('🎉 Device activated successfully!');
    print('📱 Activation Code: ${await getDeviceActivationCode()}');
    print('🆔 Device ID: ${await getDeviceId()}');
  }
  
  /// Deactivate device
  Future<void> deactivateDevice() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isActivatedKey, false);
  }
  
  /// Get device activation status info
  Future<Map<String, dynamic>> getActivationInfo() async {
    final activationCode = await getDeviceActivationCode();
    final deviceId = await getDeviceId();
    final isActivated = await isDeviceActivated();
    
    return {
      'activationCode': activationCode,
      'deviceId': deviceId,
      'isActivated': isActivated,
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }
  
  /// Reset activation (for testing purposes)
  Future<void> resetActivation() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_activationCodeKey);
    await prefs.remove(_deviceIdKey);
    await prefs.remove(_isActivatedKey);
  }
  
  /// Validate activation code format
  bool isValidActivationCode(String code) {
    // Check if code is 6 digits
    if (code.length != 6) return false;
    
    // Check if all characters are digits
    return RegExp(r'^\d{6}$').hasMatch(code);
  }
  
  /// Generate QR code data for activation
  Future<String> getQRCodeData() async {
    final activationInfo = await getActivationInfo();
    return jsonEncode({
      'app': 'دائن مدين',
      'type': 'activation',
      'code': activationInfo['activationCode'],
      'device': activationInfo['deviceId'],
      'timestamp': activationInfo['generatedAt'],
    });
  }
}
