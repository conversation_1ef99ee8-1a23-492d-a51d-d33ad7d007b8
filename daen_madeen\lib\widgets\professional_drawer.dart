import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../screens/customer/debts_screen.dart';
import '../screens/customer/orders_screen.dart';
import '../screens/customer/remote_debt_screen.dart';
import '../screens/customer/profile_screen.dart';

class ProfessionalDrawer extends StatelessWidget {
  const ProfessionalDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: Column(
        children: [
          _buildDrawerHeader(context),
          Expanded(
            child: _buildDrawerBody(context),
          ),
          _buildDrawerFooter(context),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final customer = authProvider.userProfile;
        
        return Container(
          height: 200,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF0A0E27), Color(0xFF1A1F3A)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Safe<PERSON>rea(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: Colors.white.withOpacity(0.2),
                        child: Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 35,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              customer?.name ?? 'المستخدم',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '@${customer?.username ?? 'username'}',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.verified_user,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'عميل مُفعل',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDrawerBody(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 8),
      children: [
        _buildDrawerSection(
          title: 'الصفحات الرئيسية',
          items: [
            _DrawerItem(
              icon: Icons.dashboard,
              title: 'لوحة التحكم',
              subtitle: 'الصفحة الرئيسية',
              onTap: () {
                Navigator.pop(context);
              },
            ),
            _DrawerItem(
              icon: Icons.receipt_long,
              title: 'الديون',
              subtitle: 'عرض وإدارة الديون',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DebtsScreen(),
                  ),
                );
              },
            ),
            _DrawerItem(
              icon: Icons.inventory_2,
              title: 'الطلبات',
              subtitle: 'متابعة الطلبات',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const OrdersScreen(),
                  ),
                );
              },
            ),
          ],
        ),
        const Divider(height: 32),
        _buildDrawerSection(
          title: 'العمليات',
          items: [
            _DrawerItem(
              icon: Icons.edit_note,
              title: 'تسجيل دين من بعد',
              subtitle: 'إضافة دين جديد أو سداد',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RemoteDebtScreen(),
                  ),
                );
              },
            ),
            _DrawerItem(
              icon: Icons.notifications,
              title: 'الإشعارات',
              subtitle: 'عرض الإشعارات',
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('صفحة الإشعارات قريباً')),
                );
              },
            ),
          ],
        ),
        const Divider(height: 32),
        _buildDrawerSection(
          title: 'الحساب',
          items: [
            _DrawerItem(
              icon: Icons.person,
              title: 'الملف الشخصي',
              subtitle: 'إعدادات الحساب',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                );
              },
            ),
            _DrawerItem(
              icon: Icons.settings,
              title: 'الإعدادات',
              subtitle: 'إعدادات التطبيق',
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('الإعدادات قريباً')),
                );
              },
            ),
            _DrawerItem(
              icon: Icons.help_outline,
              title: 'المساعدة',
              subtitle: 'الأسئلة الشائعة',
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('المساعدة قريباً')),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDrawerSection({
    required String title,
    required List<_DrawerItem> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Text(
            title,
            style: const TextStyle(
              color: Color(0xFF7F8C8D),
              fontSize: 12,
              fontWeight: FontWeight.bold,
              letterSpacing: 1.2,
            ),
          ),
        ),
        ...items.map((item) => _buildDrawerItem(item)),
      ],
    );
  }

  Widget _buildDrawerItem(_DrawerItem item) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFF4facfe).withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            item.icon,
            color: const Color(0xFF4facfe),
            size: 20,
          ),
        ),
        title: Text(
          item.title,
          style: const TextStyle(
            color: Color(0xFF2C3E50),
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          item.subtitle,
          style: const TextStyle(
            color: Color(0xFF7F8C8D),
            fontSize: 12,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Color(0xFF7F8C8D),
          size: 16,
        ),
        onTap: item.onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        hoverColor: const Color(0xFF4facfe).withOpacity(0.05),
        splashColor: const Color(0xFF4facfe).withOpacity(0.1),
      ),
    );
  }

  Widget _buildDrawerFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        border: Border(
          top: BorderSide(
            color: const Color(0xFFE9ECEF),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          _buildDrawerItem(
            _DrawerItem(
              icon: Icons.contact_support,
              title: 'تواصل معنا',
              subtitle: 'الدعم الفني',
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تواصل معنا قريباً')),
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          _buildDrawerItem(
            _DrawerItem(
              icon: Icons.logout,
              title: 'تسجيل الخروج',
              subtitle: 'الخروج من الحساب',
              onTap: () async {
                Navigator.pop(context);
                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                await authProvider.signOut();
              },
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'دائن مدين - الإصدار 1.0.0',
            style: TextStyle(
              color: const Color(0xFF7F8C8D),
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _DrawerItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  _DrawerItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });
}
