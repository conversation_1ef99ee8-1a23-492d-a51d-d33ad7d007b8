import 'package:uuid/uuid.dart';

/// Employee model for دائن مدين (Creditor-Debtor) system
class Employee {
  final String id;
  final String businessOwnerId;
  final String authUserId;
  final String name;
  final String? phone;
  final String? email;
  final String position; // المنصب
  final double salary; // الراتب
  final String? address;
  final String? nationalId; // رقم الهوية
  final DateTime hireDate; // تاريخ التوظيف
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Employee({
    required this.id,
    required this.businessOwnerId,
    required this.authUserId,
    required this.name,
    this.phone,
    this.email,
    required this.position,
    required this.salary,
    this.address,
    this.nationalId,
    required this.hireDate,
    required this.isActive,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Employee from JSON (from Supabase)
  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      id: json['id'] as String,
      businessOwnerId: json['business_owner_id'] as String,
      authUserId: json['auth_user_id'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      position: json['position'] as String,
      salary: (json['salary'] as num?)?.toDouble() ?? 0.0,
      address: json['address'] as String?,
      nationalId: json['national_id'] as String?,
      hireDate: DateTime.parse(json['hire_date'] as String),
      isActive: json['is_active'] as bool? ?? true,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert Employee to JSON (for Supabase)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'business_owner_id': businessOwnerId,
      'auth_user_id': authUserId,
      'name': name,
      'phone': phone,
      'email': email,
      'position': position,
      'salary': salary,
      'address': address,
      'national_id': nationalId,
      'hire_date': hireDate.toIso8601String(),
      'is_active': isActive,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a new Employee for insertion (without timestamps)
  Map<String, dynamic> toInsertJson() {
    return {
      'business_owner_id': businessOwnerId,
      'auth_user_id': authUserId,
      'name': name,
      'phone': phone,
      'email': email,
      'position': position,
      'salary': salary,
      'address': address,
      'national_id': nationalId,
      'hire_date': hireDate.toIso8601String(),
      'is_active': isActive,
      'notes': notes,
    };
  }

  /// Create a copy with updated fields
  Employee copyWith({
    String? id,
    String? businessOwnerId,
    String? authUserId,
    String? name,
    String? phone,
    String? email,
    String? position,
    double? salary,
    String? address,
    String? nationalId,
    DateTime? hireDate,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      businessOwnerId: businessOwnerId ?? this.businessOwnerId,
      authUserId: authUserId ?? this.authUserId,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      position: position ?? this.position,
      salary: salary ?? this.salary,
      address: address ?? this.address,
      nationalId: nationalId ?? this.nationalId,
      hireDate: hireDate ?? this.hireDate,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create an Employee for new registration
  factory Employee.create({
    required String businessOwnerId,
    required String authUserId,
    required String name,
    String? phone,
    String? email,
    required String position,
    required double salary,
    String? address,
    String? nationalId,
    DateTime? hireDate,
    bool isActive = true,
    String? notes,
  }) {
    final now = DateTime.now();
    return Employee(
      id: const Uuid().v4(),
      businessOwnerId: businessOwnerId,
      authUserId: authUserId,
      name: name,
      phone: phone,
      email: email,
      position: position,
      salary: salary,
      address: address,
      nationalId: nationalId,
      hireDate: hireDate ?? now,
      isActive: isActive,
      notes: notes,
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  String toString() {
    return 'Employee(id: $id, name: $name, position: $position, salary: $salary)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Employee && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Get formatted salary
  String get formattedSalary {
    return '${salary.toStringAsFixed(0)} ريال';
  }

  /// Get formatted hire date
  String get formattedHireDate {
    return '${hireDate.day}/${hireDate.month}/${hireDate.year}';
  }

  /// Get employment duration in months
  int get employmentDurationInMonths {
    final now = DateTime.now();
    return ((now.difference(hireDate).inDays) / 30).round();
  }

  /// Get employment duration as formatted string
  String get formattedEmploymentDuration {
    final months = employmentDurationInMonths;
    if (months < 12) {
      return '$months شهر';
    } else {
      final years = (months / 12).floor();
      final remainingMonths = months % 12;
      if (remainingMonths == 0) {
        return '$years سنة';
      } else {
        return '$years سنة و $remainingMonths شهر';
      }
    }
  }
}
