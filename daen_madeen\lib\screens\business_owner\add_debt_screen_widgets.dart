import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';

/// Widget extensions for AddDebtScreen
extension AddDebtScreenWidgets on State {
  Widget buildHeaderCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.account_balance_wallet,
            color: Colors.white,
            size: 40,
          ),
          const SizedBox(height: 12),
          const Text(
            'تسجيل دين جديد',
            style: TextStyle(
              color: Colors.white,
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 8),
          Text(
            'سجل الديون بسهولة مع إمكانية إرفاق الصور',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14,
            ),
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  Widget buildPersonSearch({
    required TextEditingController searchController,
    required List<dynamic> searchResults,
    required bool isSearching,
    required String selectedPersonName,
    required Function(String, String) onPersonSelected,
    required String selectedType,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            selectedType == 'customer' ? 'اختيار العميل' : 'اختيار الموظف',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D3561),
            ),
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 16),
          
          // Search Field
          TextFormField(
            controller: searchController,
            textDirection: TextDirection.rtl,
            decoration: InputDecoration(
              hintText: selectedType == 'customer' 
                  ? 'ابحث عن العميل...' 
                  : 'ابحث عن الموظف...',
              hintTextDirection: TextDirection.rtl,
              prefixIcon: isSearching 
                  ? const Padding(
                      padding: EdgeInsets.all(12),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : const Icon(Icons.search),
              suffixIcon: selectedPersonName.isNotEmpty
                  ? const Icon(Icons.check_circle, color: Color(0xFF4CAF50))
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF667eea), width: 2),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
          ),
          
          // Selected Person Display
          if (selectedPersonName.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.person, color: Color(0xFF4CAF50), size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تم اختيار: $selectedPersonName',
                      style: const TextStyle(
                        color: Color(0xFF4CAF50),
                        fontWeight: FontWeight.bold,
                      ),
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          // Search Results
          if (searchResults.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: searchResults.length,
                itemBuilder: (context, index) {
                  final person = searchResults[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: const Color(0xFF667eea).withOpacity(0.1),
                      child: Text(
                        person.name[0].toUpperCase(),
                        style: const TextStyle(
                          color: Color(0xFF667eea),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      person.name,
                      textDirection: TextDirection.rtl,
                    ),
                    subtitle: Text(
                      person.phone ?? '',
                      textDirection: TextDirection.rtl,
                    ),
                    onTap: () => onPersonSelected(person.id, person.name),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget buildAmountInput({
    required TextEditingController amountController,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'مبلغ الدين',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D3561),
            ),
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: amountController,
            keyboardType: TextInputType.number,
            textDirection: TextDirection.rtl,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
            ],
            decoration: InputDecoration(
              hintText: '0.00',
              hintTextDirection: TextDirection.rtl,
              prefixIcon: const Icon(Icons.attach_money),
              suffixText: 'ريال',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF667eea), width: 2),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال مبلغ الدين';
              }
              if (double.tryParse(value) == null) {
                return 'يرجى إدخال مبلغ صحيح';
              }
              if (double.parse(value) <= 0) {
                return 'يجب أن يكون المبلغ أكبر من صفر';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget buildDescriptionInput({
    required TextEditingController descriptionController,
    required VoidCallback onCameraPressed,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'تفاصيل الدين',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D3561),
                ),
                textDirection: TextDirection.rtl,
              ),
              IconButton(
                onPressed: onCameraPressed,
                icon: const Icon(Icons.camera_alt),
                color: const Color(0xFF667eea),
                tooltip: 'إضافة صورة',
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: descriptionController,
            maxLines: 4,
            textDirection: TextDirection.rtl,
            decoration: InputDecoration(
              hintText: 'اكتب تفاصيل الدين هنا...\nيمكنك إضافة صور للأشياء المأخوذة',
              hintTextDirection: TextDirection.rtl,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF667eea), width: 2),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال تفاصيل الدين';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
}
