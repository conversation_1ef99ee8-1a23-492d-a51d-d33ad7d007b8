import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // Sample notifications data
  final List<NotificationItem> _notifications = [
    NotificationItem(
      id: '1',
      title: 'تم إضافة دين جديد',
      message: 'تم إضافة دين بقيمة 500 ريال من متجر الإلكترونيات',
      type: NotificationType.debt,
      timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
      isRead: false,
    ),
    NotificationItem(
      id: '2',
      title: 'تذكير بموعد السداد',
      message: 'يحين موعد سداد دين بقيمة 1200 ريال خلال 3 أيام',
      type: NotificationType.reminder,
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      isRead: false,
    ),
    NotificationItem(
      id: '3',
      title: 'تم تأكيد الدفع',
      message: 'تم تأكيد دفع مبلغ 300 ريال بنجاح',
      type: NotificationType.payment,
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      isRead: true,
    ),
    NotificationItem(
      id: '4',
      title: 'عرض خاص',
      message: 'خصم 20% على جميع المنتجات لفترة محدودة',
      type: NotificationType.offer,
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      isRead: true,
    ),
    NotificationItem(
      id: '5',
      title: 'تحديث في الحساب',
      message: 'تم تحديث معلومات حسابك بنجاح',
      type: NotificationType.account,
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      isRead: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white, // خلفية بيضاء
      appBar: AppBar(
        title: const Text(
          'الإشعارات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white, // نص أبيض
          ),
        ),
        backgroundColor: const Color(0xFF0A0E27), // خلفية داكنة
        elevation: 0,
        centerTitle: true,
        automaticallyImplyLeading: false, // إزالة السهم التلقائي
        actions: [
          // السهم في الجهة اليمين
          Container(
            margin: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
              ),
            ),
            child: IconButton(
              padding: EdgeInsets.zero,
              icon: const Icon(Icons.arrow_back, color: Colors.white, size: 20),
              onPressed: () => Navigator.pop(context),
            ),
          ),
        ],
        leading: Container(
          margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
            ),
          ),
          child: IconButton(
            padding: EdgeInsets.zero,
            icon: const Icon(Icons.mark_email_read, color: Colors.white, size: 20),
            onPressed: _markAllAsRead,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: _buildNotificationsList(),
      ),
    );
  }

  Widget _buildNotificationsList() {
    final unreadNotifications = _notifications.where((n) => !n.isRead).toList();
    final readNotifications = _notifications.where((n) => n.isRead).toList();

    return CustomScrollView(
      slivers: [
        // Unread notifications section
        if (unreadNotifications.isNotEmpty) ...[
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE74C3C),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'إشعارات جديدة',
                    style: TextStyle(
                      color: Color(0xFF2C3E50), // نص داكن
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE74C3C),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${unreadNotifications.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => _buildNotificationCard(unreadNotifications[index]),
              childCount: unreadNotifications.length,
            ),
          ),
        ],

        // Read notifications section
        if (readNotifications.isNotEmpty) ...[
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: const Color(0xFF7F8C8D),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'إشعارات سابقة',
                    style: TextStyle(
                      color: Color(0xFF7F8C8D), // رمادي يبقى كما هو
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => _buildNotificationCard(readNotifications[index]),
              childCount: readNotifications.length,
            ),
          ),
        ],

        // Empty state
        if (_notifications.isEmpty)
          SliverFillRemaining(
            child: _buildEmptyState(),
          ),
      ],
    );
  }

  Widget _buildNotificationCard(NotificationItem notification) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        color: notification.isRead
            ? const Color(0xFFF8F9FA) // رمادي فاتح جداً للمقروء
            : Colors.white, // أبيض للجديد
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: notification.isRead
              ? const Color(0xFFE9ECEF) // حدود رمادية فاتحة للمقروء
              : _getNotificationColor(notification.type).withOpacity(0.3),
          width: notification.isRead ? 1 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(15),
          onTap: () => _markAsRead(notification.id),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Notification icon
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: _getNotificationColor(notification.type).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: _getNotificationColor(notification.type),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Notification content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: TextStyle(
                                color: notification.isRead
                                    ? const Color(0xFF7F8C8D) // رمادي للمقروء
                                    : const Color(0xFF2C3E50), // داكن للجديد
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Color(0xFFE74C3C),
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        notification.message,
                        style: TextStyle(
                          color: notification.isRead
                              ? const Color(0xFF95A5A6) // رمادي للمقروء
                              : const Color(0xFF5D6D7E), // رمادي داكن للجديد
                          fontSize: 14,
                          height: 1.4,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        _formatTimestamp(notification.timestamp),
                        style: TextStyle(
                          color: notification.isRead
                              ? const Color(0xFF7F8C8D) // رمادي للمقروء
                              : const Color(0xFF95A5A6), // رمادي فاتح للجديد
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA), // رمادي فاتح
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.notifications_off,
              color: Color(0xFF7F8C8D),
              size: 60,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'لا توجد إشعارات',
            style: TextStyle(
              color: Color(0xFF2C3E50), // نص داكن
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'ستظهر هنا جميع الإشعارات والتحديثات',
            style: TextStyle(
              color: Color(0xFF7F8C8D), // رمادي
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.debt:
        return const Color(0xFFE74C3C); // أحمر للديون
      case NotificationType.payment:
        return const Color(0xFF27AE60); // أخضر للمدفوعات
      case NotificationType.reminder:
        return const Color(0xFFF39C12); // برتقالي للتذكيرات
      case NotificationType.offer:
        return const Color(0xFF9B59B6); // بنفسجي للعروض
      case NotificationType.account:
        return const Color(0xFF3498DB); // أزرق للحساب
    }
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.debt:
        return Icons.receipt_long;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.reminder:
        return Icons.schedule;
      case NotificationType.offer:
        return Icons.local_offer;
      case NotificationType.account:
        return Icons.account_circle;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  void _markAsRead(String notificationId) {
    setState(() {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
      }
    });
  }

  void _markAllAsRead() {
    setState(() {
      for (int i = 0; i < _notifications.length; i++) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تمييز جميع الإشعارات كمقروءة'),
        backgroundColor: Color(0xFF27AE60),
      ),
    );
  }
}

// Notification models
enum NotificationType {
  debt,
  payment,
  reminder,
  offer,
  account,
}

class NotificationItem {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final bool isRead;

  const NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    required this.isRead,
  });

  NotificationItem copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    bool? isRead,
  }) {
    return NotificationItem(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
    );
  }
}
