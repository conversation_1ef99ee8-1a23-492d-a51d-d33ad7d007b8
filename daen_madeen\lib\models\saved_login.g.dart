// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'saved_login.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SavedLoginAdapter extends TypeAdapter<SavedLogin> {
  @override
  final int typeId = 6;

  @override
  SavedLogin read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SavedLogin(
      id: fields[0] as String,
      storeName: fields[1] as String,
      username: fields[2] as String,
      password: fields[3] as String,
      createdAt: fields[4] as DateTime,
      lastUsed: fields[5] as DateTime,
      storeDescription: fields[6] as String?,
      isFavorite: fields[7] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, SavedLogin obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.storeName)
      ..writeByte(2)
      ..write(obj.username)
      ..writeByte(3)
      ..write(obj.password)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.lastUsed)
      ..writeByte(6)
      ..write(obj.storeDescription)
      ..writeByte(7)
      ..write(obj.isFavorite);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SavedLoginAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
