import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../../providers/auth_provider.dart';
import '../../models/business_owner.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordFormKey = GlobalKey<FormState>();

  // Profile controllers
  final _businessNameController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _profilePhoneController = TextEditingController();
  final _profileEmailController = TextEditingController();
  final _addressController = TextEditingController();

  // Password controllers
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // Login info controllers (read-only)
  final _loginEmailController = TextEditingController();
  final _loginPhoneController = TextEditingController();

  File? _imageFile;
  Uint8List? _webImage;
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;
  bool _isPasswordChangeMode = false;
  bool _isPasswordLoading = false;
  bool _showCurrentPassword = false;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  void _loadUserData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.userProfile;

    if (user != null) {
      // Profile data (editable)
      _businessNameController.text = user.businessName ?? '';
      _ownerNameController.text = user.ownerName ?? '';
      _profilePhoneController.text = user.phone ?? '';
      _profileEmailController.text = user.email ?? '';
      _addressController.text = user.address ?? '';

      // Login data (read-only display)
      _loginEmailController.text = user.email ?? '';
      _loginPhoneController.text = user.phone ?? '';
    }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );
      
      if (image != null) {
        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          setState(() {
            _webImage = bytes;
          });
        } else {
          setState(() {
            _imageFile = File(image.path);
          });
        }
      }
    } catch (e) {
      _showErrorSnackBar('فشل في اختيار الصورة: $e');
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.userProfile;
      
      if (currentUser != null) {
        // تحويل الصورة إلى base64 إذا تم اختيارها
        String? imageData;
        if (kIsWeb && _webImage != null) {
          imageData = 'data:image/png;base64,${base64Encode(_webImage!)}';
        } else if (!kIsWeb && _imageFile != null) {
          final bytes = await _imageFile!.readAsBytes();
          imageData = 'data:image/png;base64,${base64Encode(bytes)}';
        }

        // إنشاء نسخة محدثة من المستخدم
        final updatedUser = BusinessOwner(
          id: currentUser.id,
          authUserId: currentUser.authUserId,
          businessName: _businessNameController.text.trim(),
          ownerName: _ownerNameController.text.trim(),
          email: _profileEmailController.text.trim(),
          phone: _profilePhoneController.text.trim(),
          address: _addressController.text.trim(),
          profileImagePath: imageData ?? currentUser.profileImagePath,
          createdAt: currentUser.createdAt,
          updatedAt: DateTime.now(),
        );

        // حفظ البيانات المحدثة
        print('🔄 بدء حفظ البيانات...');
        print('📝 البيانات الجديدة: ${updatedUser.businessName}');
        print('📷 الصورة: ${updatedUser.profileImagePath != null ? "موجودة" : "غير موجودة"}');

        await authProvider.updateUserProfile(updatedUser);

        print('✅ تم حفظ البيانات بنجاح');
        _showSuccessSnackBar('تم حفظ البيانات بنجاح');

        // انتظار قليل للتأكد من التحديث
        await Future.delayed(const Duration(milliseconds: 500));

        Navigator.pop(context);
      }
    } catch (e) {
      _showErrorSnackBar('فشل في حفظ البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _togglePasswordChangeMode() {
    setState(() {
      _isPasswordChangeMode = !_isPasswordChangeMode;
      if (!_isPasswordChangeMode) {
        // Clear password fields when closing
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmPasswordController.clear();
      }
    });
  }

  Future<void> _changePassword() async {
    if (!_passwordFormKey.currentState!.validate()) return;

    setState(() {
      _isPasswordLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Simulate password change (replace with actual implementation)
      await Future.delayed(const Duration(seconds: 2));

      // Here you would implement actual password change logic
      // For now, we'll just show success message

      _showSuccessSnackBar('تم تغيير كلمة المرور بنجاح');
      _togglePasswordChangeMode();

    } catch (e) {
      _showErrorSnackBar('فشل في تغيير كلمة المرور: $e');
    } finally {
      setState(() {
        _isPasswordLoading = false;
      });
    }
  }

  @override
  void dispose() {
    // Profile controllers
    _businessNameController.dispose();
    _ownerNameController.dispose();
    _profilePhoneController.dispose();
    _profileEmailController.dispose();
    _addressController.dispose();

    // Password controllers
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();

    // Login info controllers
    _loginEmailController.dispose();
    _loginPhoneController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'الملف الشخصي',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF0A0E27),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // صورة الملف الشخصي
              _buildProfileImageSection(),
              
              const SizedBox(height: 30),
              
              // معلومات المحل
              _buildSectionTitle('معلومات المحل'),
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _businessNameController,
                label: 'اسم المحل',
                icon: Icons.store,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم المحل';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _addressController,
                label: 'عنوان المحل',
                icon: Icons.location_on,
                maxLines: 2,
              ),
              
              const SizedBox(height: 30),
              
              // معلومات المالك
              _buildSectionTitle('معلومات المالك'),
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _ownerNameController,
                label: 'اسم المالك',
                icon: Icons.person,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم المالك';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),

              _buildTextField(
                controller: _profilePhoneController,
                label: 'رقم الهاتف (للعرض)',
                icon: Icons.phone,
                keyboardType: TextInputType.phone,
              ),

              const SizedBox(height: 16),

              _buildTextField(
                controller: _profileEmailController,
                label: 'البريد الإلكتروني (للعرض)',
                icon: Icons.email,
                keyboardType: TextInputType.emailAddress,
              ),

              const SizedBox(height: 30),

              // معلومات تسجيل الدخول
              _buildSectionTitle('معلومات تسجيل الدخول'),
              const SizedBox(height: 16),

              _buildReadOnlyTextField(
                controller: _loginEmailController,
                label: 'البريد الإلكتروني (تسجيل الدخول)',
                icon: Icons.email_outlined,
                subtitle: 'لا يمكن تغييره - للدخول فقط',
              ),

              const SizedBox(height: 16),

              _buildReadOnlyTextField(
                controller: _loginPhoneController,
                label: 'رقم الهاتف (تسجيل الدخول)',
                icon: Icons.phone_outlined,
                subtitle: 'لا يمكن تغييره - للدخول فقط',
              ),

              const SizedBox(height: 30),

              // قسم تغيير كلمة المرور
              _buildPasswordSection(),

              const SizedBox(height: 40),

              // زر الحفظ
              _buildSaveButton(),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileImageSection() {
    return Center(
      child: Column(
        children: [
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(60),
                border: Border.all(
                  color: const Color(0xFF0A0E27),
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: _buildImageWidget(),
            ),
          ),
          const SizedBox(height: 12),
          TextButton.icon(
            onPressed: _pickImage,
            icon: const Icon(Icons.camera_alt, color: Color(0xFF0A0E27)),
            label: const Text(
              'تغيير صورة المحل',
              style: TextStyle(
                color: Color(0xFF0A0E27),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageWidget() {
    // إذا تم اختيار صورة جديدة
    if (kIsWeb && _webImage != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(57),
        child: Image.memory(
          _webImage!,
          width: 114,
          height: 114,
          fit: BoxFit.cover,
        ),
      );
    } else if (!kIsWeb && _imageFile != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(57),
        child: Image.file(
          _imageFile!,
          width: 114,
          height: 114,
          fit: BoxFit.cover,
        ),
      );
    } else {
      // إذا لم يتم اختيار صورة جديدة، تحقق من الصورة المحفوظة
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.userProfile;

      if (user?.profileImagePath != null && user!.profileImagePath!.isNotEmpty) {
        // عرض الصورة المحفوظة
        if (user.profileImagePath!.startsWith('data:image')) {
          // صورة base64
          final base64String = user.profileImagePath!.split(',')[1];
          final bytes = base64Decode(base64String);
          return ClipRRect(
            borderRadius: BorderRadius.circular(57),
            child: Image.memory(
              bytes,
              width: 114,
              height: 114,
              fit: BoxFit.cover,
            ),
          );
        }
      }

      // عرض الأيقونة الافتراضية
      return const Icon(
        Icons.store,
        size: 50,
        color: Color(0xFF0A0E27),
      );
    }
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Color(0xFF0A0E27),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        maxLines: maxLines,
        validator: validator,
        textDirection: TextDirection.rtl,
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: Icon(icon, color: const Color(0xFF0A0E27)),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          labelStyle: const TextStyle(
            color: Color(0xFF6B7280),
          ),
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF0A0E27),
            Color(0xFF1A1F3A),
            Color(0xFF2D3561),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF0A0E27).withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveProfile,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'حفظ التغييرات',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildReadOnlyTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? subtitle,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.grey.shade600),
        ),
        title: Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
          textDirection: TextDirection.rtl,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              controller.text.isEmpty ? 'غير محدد' : controller.text,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
              textDirection: TextDirection.rtl,
            ),
            if (subtitle != null)
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
                textDirection: TextDirection.rtl,
              ),
          ],
        ),
        trailing: const Icon(Icons.lock, color: Colors.grey),
      ),
    );
  }

  Widget _buildPasswordSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with toggle button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFE53E3E).withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              textDirection: TextDirection.rtl,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE53E3E).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.lock, color: Color(0xFFE53E3E), size: 24),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'تغيير كلمة المرور',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFE53E3E),
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                ),
                Switch(
                  value: _isPasswordChangeMode,
                  onChanged: (value) => _togglePasswordChangeMode(),
                  activeColor: const Color(0xFFE53E3E),
                ),
              ],
            ),
          ),

          // Password fields (shown when toggle is on)
          if (_isPasswordChangeMode) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _passwordFormKey,
                child: Column(
                  children: [
                    _buildPasswordField(
                      controller: _currentPasswordController,
                      label: 'كلمة المرور الحالية',
                      isVisible: _showCurrentPassword,
                      onToggleVisibility: () => setState(() => _showCurrentPassword = !_showCurrentPassword),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال كلمة المرور الحالية';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    _buildPasswordField(
                      controller: _newPasswordController,
                      label: 'كلمة المرور الجديدة',
                      isVisible: _showNewPassword,
                      onToggleVisibility: () => setState(() => _showNewPassword = !_showNewPassword),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال كلمة المرور الجديدة';
                        }
                        if (value.length < 6) {
                          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    _buildPasswordField(
                      controller: _confirmPasswordController,
                      label: 'تأكيد كلمة المرور الجديدة',
                      isVisible: _showConfirmPassword,
                      onToggleVisibility: () => setState(() => _showConfirmPassword = !_showConfirmPassword),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى تأكيد كلمة المرور الجديدة';
                        }
                        if (value != _newPasswordController.text) {
                          return 'كلمة المرور غير متطابقة';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 24),

                    // Change password button
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFFE53E3E), Color(0xFFFC8181)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFFE53E3E).withOpacity(0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ElevatedButton.icon(
                          onPressed: _isPasswordLoading ? null : _changePassword,
                          icon: _isPasswordLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(Icons.save, color: Colors.white),
                          label: Text(
                            _isPasswordLoading ? 'جاري التغيير...' : 'تغيير كلمة المرور',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'قم بتفعيل المفتاح أعلاه لتغيير كلمة المرور',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required bool isVisible,
    required VoidCallback onToggleVisibility,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: !isVisible,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(isVisible ? Icons.visibility : Icons.visibility_off),
          onPressed: onToggleVisibility,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFE53E3E), width: 2),
        ),
      ),
      textDirection: TextDirection.rtl,
    );
  }
}
