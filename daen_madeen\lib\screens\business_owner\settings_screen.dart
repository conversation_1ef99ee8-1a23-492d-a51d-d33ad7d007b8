import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../services/local_storage_service.dart';
import '../../services/device_activation_service.dart';
import '../../utils/supabase_config.dart';
import 'security_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  // Settings values
  bool _enableNotifications = true;
  bool _enableBackupReminder = true;
  bool _enableAutoBackup = false;
  bool _enableSoundEffects = true;
  bool _enableHapticFeedback = true;
  bool _enableDarkMode = false;
  String _backupFrequency = 'weekly';
  String _currency = 'SAR';
  String _language = 'ar';
  int _reminderDays = 7;
  double _lowStockThreshold = 10.0;
  
  // Device info
  String _deviceId = '';
  String _activationCode = '';
  bool _isActivated = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack));
    
    _animationController.forward();
    _loadSettings();
    _loadDeviceInfo();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _enableNotifications = LocalStorageService.getSetting('enable_notifications', defaultValue: true);
      _enableBackupReminder = LocalStorageService.getSetting('enable_backup_reminder', defaultValue: true);
      _enableAutoBackup = LocalStorageService.getSetting('enable_auto_backup', defaultValue: false);
      _enableSoundEffects = LocalStorageService.getSetting('enable_sound_effects', defaultValue: true);
      _enableHapticFeedback = LocalStorageService.getSetting('enable_haptic_feedback', defaultValue: true);
      _enableDarkMode = LocalStorageService.getSetting('enable_dark_mode', defaultValue: false);
      _backupFrequency = LocalStorageService.getSetting('backup_frequency', defaultValue: 'weekly');
      _currency = LocalStorageService.getSetting('currency', defaultValue: 'SAR');
      _language = LocalStorageService.getSetting('language', defaultValue: 'ar');
      _reminderDays = LocalStorageService.getSetting('reminder_days', defaultValue: 7);
      _lowStockThreshold = LocalStorageService.getSetting('low_stock_threshold', defaultValue: 10.0);
    });
  }

  Future<void> _loadDeviceInfo() async {
    try {
      final activationService = DeviceActivationService.instance;
      final deviceId = await activationService.getDeviceId();
      final activationCode = await activationService.getDeviceActivationCode();
      final isActivated = await activationService.isDeviceActivated();

      setState(() {
        _deviceId = deviceId;
        _activationCode = activationCode;
        _isActivated = isActivated;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSetting(String key, dynamic value) async {
    await LocalStorageService.saveSetting(key, value);
    if (key == 'enable_haptic_feedback' && value == true) {
      HapticFeedback.lightImpact();
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text(
          'الإعدادات',
          style: TextStyle(
            fontWeight: FontWeight.bold, 
            fontSize: 22,
            color: Colors.white,
            letterSpacing: 0.5,
          ),
        ),
        backgroundColor: const Color(0xFF6C63FF),
        foregroundColor: Colors.white,
        elevation: 8,
        shadowColor: const Color(0xFF6C63FF).withOpacity(0.3),
        centerTitle: true,
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
            onPressed: () => Navigator.pop(context),
            iconSize: 20,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF6C63FF), Color(0xFF8B7CF6)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6C63FF), Color(0xFF8B7CF6)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF6C63FF).withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: const Column(
                    children: [
                      Icon(
                        Icons.settings,
                        size: 48,
                        color: Colors.white,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'إعدادات التطبيق',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'تخصيص التطبيق حسب احتياجاتك',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                        ),
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Device Information Section
                _buildSectionCard(
                  title: 'معلومات الجهاز',
                  icon: Icons.phone_android,
                  color: const Color(0xFF4CAF50),
                  children: [
                    _buildInfoTile(
                      'معرف الجهاز',
                      _deviceId.isNotEmpty ? _deviceId : 'غير متوفر',
                      Icons.fingerprint,
                      onTap: () => _copyToClipboard(_deviceId, 'تم نسخ معرف الجهاز'),
                    ),
                    _buildInfoTile(
                      'رمز التفعيل',
                      _activationCode.isNotEmpty ? _activationCode : 'غير متوفر',
                      Icons.vpn_key,
                      onTap: () => _copyToClipboard(_activationCode, 'تم نسخ رمز التفعيل'),
                    ),
                    _buildInfoTile(
                      'حالة التفعيل',
                      _isActivated ? 'مُفعّل' : 'غير مُفعّل',
                      _isActivated ? Icons.check_circle : Icons.cancel,
                      color: _isActivated ? Colors.green : Colors.red,
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Notifications Section
                _buildSectionCard(
                  title: 'الإشعارات',
                  icon: Icons.notifications,
                  color: const Color(0xFFFF9800),
                  children: [
                    _buildSwitchTile(
                      'تفعيل الإشعارات',
                      'استقبال إشعارات التطبيق',
                      _enableNotifications,
                      (value) {
                        setState(() => _enableNotifications = value);
                        _saveSetting('enable_notifications', value);
                      },
                    ),
                    _buildSwitchTile(
                      'تذكير النسخ الاحتياطي',
                      'تذكير دوري لعمل نسخة احتياطية',
                      _enableBackupReminder,
                      (value) {
                        setState(() => _enableBackupReminder = value);
                        _saveSetting('enable_backup_reminder', value);
                      },
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Backup Section
                _buildSectionCard(
                  title: 'النسخ الاحتياطي',
                  icon: Icons.backup,
                  color: const Color(0xFF2196F3),
                  children: [
                    _buildSwitchTile(
                      'النسخ الاحتياطي التلقائي',
                      'إنشاء نسخة احتياطية تلقائياً',
                      _enableAutoBackup,
                      (value) {
                        setState(() => _enableAutoBackup = value);
                        _saveSetting('enable_auto_backup', value);
                      },
                    ),
                    _buildDropdownTile(
                      'تكرار النسخ الاحتياطي',
                      _backupFrequency,
                      {
                        'daily': 'يومياً',
                        'weekly': 'أسبوعياً',
                        'monthly': 'شهرياً',
                      },
                      (value) {
                        setState(() => _backupFrequency = value!);
                        _saveSetting('backup_frequency', value);
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // User Experience Section
                _buildSectionCard(
                  title: 'تجربة المستخدم',
                  icon: Icons.tune,
                  color: const Color(0xFF9C27B0),
                  children: [
                    _buildSwitchTile(
                      'الأصوات',
                      'تشغيل الأصوات في التطبيق',
                      _enableSoundEffects,
                      (value) {
                        setState(() => _enableSoundEffects = value);
                        _saveSetting('enable_sound_effects', value);
                      },
                    ),
                    _buildSwitchTile(
                      'الاهتزاز',
                      'تفعيل الاهتزاز عند اللمس',
                      _enableHapticFeedback,
                      (value) {
                        setState(() => _enableHapticFeedback = value);
                        _saveSetting('enable_haptic_feedback', value);
                      },
                    ),
                    _buildSwitchTile(
                      'الوضع الليلي',
                      'تفعيل الوضع المظلم (قريباً)',
                      _enableDarkMode,
                      (value) {
                        setState(() => _enableDarkMode = value);
                        _saveSetting('enable_dark_mode', value);
                        _showSnackBar('الوضع الليلي سيكون متاحاً قريباً', Colors.blue);
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Business Settings Section
                _buildSectionCard(
                  title: 'إعدادات العمل',
                  icon: Icons.business,
                  color: const Color(0xFFE91E63),
                  children: [
                    _buildDropdownTile(
                      'العملة',
                      _currency,
                      {
                        'SAR': 'ريال سعودي (ر.س)',
                        'USD': 'دولار أمريكي (\$)',
                        'EUR': 'يورو (€)',
                        'AED': 'درهم إماراتي (د.إ)',
                      },
                      (value) {
                        setState(() => _currency = value!);
                        _saveSetting('currency', value);
                      },
                    ),
                    _buildSliderTile(
                      'أيام التذكير بالديون',
                      _reminderDays.toDouble(),
                      1.0,
                      30.0,
                      (value) {
                        setState(() => _reminderDays = value.round());
                        _saveSetting('reminder_days', _reminderDays);
                      },
                      '${_reminderDays} أيام',
                    ),
                    _buildSliderTile(
                      'حد تنبيه المخزون المنخفض',
                      _lowStockThreshold,
                      1.0,
                      100.0,
                      (value) {
                        setState(() => _lowStockThreshold = value);
                        _saveSetting('low_stock_threshold', _lowStockThreshold);
                      },
                      '${_lowStockThreshold.round()} قطعة',
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Database & Sync Section
                _buildSectionCard(
                  title: 'قاعدة البيانات والمزامنة',
                  icon: Icons.cloud_sync,
                  color: const Color(0xFF607D8B),
                  children: [
                    _buildActionTile(
                      'حالة الاتصال',
                      SupabaseConfig.isConfigured ? 'متصل بالسحابة' : 'وضع محلي',
                      SupabaseConfig.isConfigured ? Icons.cloud_done : Icons.cloud_off,
                      color: SupabaseConfig.isConfigured ? Colors.green : Colors.orange,
                      onTap: () => _showConnectionStatus(),
                    ),
                    _buildActionTile(
                      'مزامنة البيانات',
                      'مزامنة البيانات مع السحابة',
                      Icons.sync,
                      onTap: () => _syncData(),
                    ),
                    _buildActionTile(
                      'تنظيف البيانات المحلية',
                      'حذف البيانات المؤقتة والمخزنة محلياً',
                      Icons.cleaning_services,
                      color: Colors.red,
                      onTap: () => _showCleanDataDialog(),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Security Section
                _buildSectionCard(
                  title: 'الأمان والخصوصية',
                  icon: Icons.security,
                  color: const Color(0xFFE53E3E),
                  children: [
                    _buildActionTile(
                      'إعدادات الأمان',
                      'قفل التطبيق وحماية البيانات',
                      Icons.lock,
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SecuritySettingsScreen(),
                        ),
                      ),
                    ),
                    _buildActionTile(
                      'النسخ الاحتياطي الآمن',
                      'تشفير وحماية النسخ الاحتياطية',
                      Icons.backup,
                      onTap: () => _showSnackBar('النسخ الاحتياطي الآمن قريباً', Colors.blue),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Advanced Settings Section
                _buildSectionCard(
                  title: 'إعدادات متقدمة',
                  icon: Icons.engineering,
                  color: const Color(0xFF795548),
                  children: [
                    _buildActionTile(
                      'تصدير البيانات',
                      'تصدير جميع البيانات كملف Excel',
                      Icons.file_download,
                      onTap: () => _exportData(),
                    ),
                    _buildActionTile(
                      'استيراد البيانات',
                      'استيراد البيانات من ملف Excel',
                      Icons.file_upload,
                      onTap: () => _importData(),
                    ),
                    _buildActionTile(
                      'إعادة تعيين الإعدادات',
                      'إعادة جميع الإعدادات للوضع الافتراضي',
                      Icons.restore,
                      color: Colors.red,
                      onTap: () => _showResetSettingsDialog(),
                    ),
                  ],
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              textDirection: TextDirection.rtl,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
        textDirection: TextDirection.rtl,
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey.shade600),
        textDirection: TextDirection.rtl,
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF6C63FF),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildDropdownTile(
    String title,
    String value,
    Map<String, String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
        textDirection: TextDirection.rtl,
      ),
      trailing: DropdownButton<String>(
        value: value,
        onChanged: onChanged,
        items: options.entries.map((entry) => DropdownMenuItem(
          value: entry.key,
          child: Text(entry.value),
        )).toList(),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildSliderTile(
    String title,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
    String displayValue,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(fontWeight: FontWeight.w600),
                textDirection: TextDirection.rtl,
              ),
              Text(
                displayValue,
                style: TextStyle(
                  color: const Color(0xFF6C63FF),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Slider(
            value: value,
            min: min,
            max: max,
            onChanged: onChanged,
            activeColor: const Color(0xFF6C63FF),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoTile(
    String title,
    String value,
    IconData icon, {
    VoidCallback? onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color ?? Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
        textDirection: TextDirection.rtl,
      ),
      subtitle: Text(
        value,
        style: TextStyle(color: Colors.grey.shade600),
        textDirection: TextDirection.rtl,
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon, {
    VoidCallback? onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color ?? Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
        textDirection: TextDirection.rtl,
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey.shade600),
        textDirection: TextDirection.rtl,
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  void _copyToClipboard(String text, String message) {
    if (text.isNotEmpty && text != 'غير متوفر') {
      Clipboard.setData(ClipboardData(text: text));
      _showSnackBar(message, Colors.green);
      if (_enableHapticFeedback) {
        HapticFeedback.lightImpact();
      }
    }
  }

  void _showConnectionStatus() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text(
          'حالة الاتصال',
          textDirection: TextDirection.rtl,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              textDirection: TextDirection.rtl,
              children: [
                Icon(
                  SupabaseConfig.isConfigured ? Icons.cloud_done : Icons.cloud_off,
                  color: SupabaseConfig.isConfigured ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  SupabaseConfig.isConfigured ? 'متصل بالسحابة' : 'وضع محلي',
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              SupabaseConfig.isConfigured
                  ? 'التطبيق متصل بقاعدة البيانات السحابية ويتم مزامنة البيانات تلقائياً.'
                  : 'التطبيق يعمل في الوضع المحلي. البيانات محفوظة على الجهاز فقط.',
              textDirection: TextDirection.rtl,
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  Future<void> _syncData() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري المزامنة...'),
          ],
        ),
      ),
    );

    await Future.delayed(const Duration(seconds: 2));
    Navigator.pop(context);
    _showSnackBar('تم مزامنة البيانات بنجاح', Colors.green);
  }

  void _showCleanDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text(
          'تنظيف البيانات',
          textDirection: TextDirection.rtl,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: const Text(
          'هل أنت متأكد من حذف البيانات المؤقتة؟ هذا الإجراء لا يمكن التراجع عنه.',
          textDirection: TextDirection.rtl,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _cleanLocalData();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _cleanLocalData() async {
    // Simulate cleaning process
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري التنظيف...'),
          ],
        ),
      ),
    );

    await Future.delayed(const Duration(seconds: 2));
    Navigator.pop(context);
    _showSnackBar('تم تنظيف البيانات المؤقتة بنجاح', Colors.green);
  }

  Future<void> _exportData() async {
    _showSnackBar('تصدير البيانات سيكون متاحاً قريباً', Colors.blue);
  }

  Future<void> _importData() async {
    _showSnackBar('استيراد البيانات سيكون متاحاً قريباً', Colors.blue);
  }

  void _showResetSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text(
          'إعادة تعيين الإعدادات',
          textDirection: TextDirection.rtl,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: const Text(
          'هل أنت متأكد من إعادة تعيين جميع الإعدادات للوضع الافتراضي؟',
          textDirection: TextDirection.rtl,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _resetSettings();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إعادة تعيين', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _resetSettings() async {
    // Reset all settings to default values
    await LocalStorageService.deleteSetting('enable_notifications');
    await LocalStorageService.deleteSetting('enable_backup_reminder');
    await LocalStorageService.deleteSetting('enable_auto_backup');
    await LocalStorageService.deleteSetting('enable_sound_effects');
    await LocalStorageService.deleteSetting('enable_haptic_feedback');
    await LocalStorageService.deleteSetting('enable_dark_mode');
    await LocalStorageService.deleteSetting('backup_frequency');
    await LocalStorageService.deleteSetting('currency');
    await LocalStorageService.deleteSetting('language');
    await LocalStorageService.deleteSetting('reminder_days');
    await LocalStorageService.deleteSetting('low_stock_threshold');

    // Reload settings
    _loadSettings();
    _showSnackBar('تم إعادة تعيين الإعدادات بنجاح', Colors.green);
  }
}
