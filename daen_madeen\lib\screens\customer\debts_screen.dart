import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/models.dart';
import '../../providers/auth_provider.dart';
import '../../services/debt_service.dart';

class DebtsScreen extends StatefulWidget {
  const DebtsScreen({super.key});

  @override
  State<DebtsScreen> createState() => _DebtsScreenState();
}

class _DebtsScreenState extends State<DebtsScreen> with TickerProviderStateMixin {
  List<Debt> _debts = [];
  bool _isLoading = true;
  String _selectedFilter = 'all'; // all, paid, unpaid
  String _searchQuery = '';
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadDebts();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200), // تقليل مدة الرسوم المتحركة
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut, // منحنى أبسط
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1), // تقليل المسافة
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut, // منحنى أبسط
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadDebts() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final customer = authProvider.userProfile;

      if (customer != null) {
        // استخدام البيانات التجريبية مباشرة لتحسين الأداء
        final debts = _generateSampleDebts(customer.id);
        setState(() {
          _debts = debts;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في تحميل الديون'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  List<Debt> _generateSampleDebts(String customerId) {
    final now = DateTime.now();
    return [
      Debt(
        id: '1',
        customerId: customerId,
        businessOwnerId: 'owner_123',
        amount: 150.0,
        description: 'مشتريات متنوعة من البقالة',
        isPaid: false,
        dateCreated: now.subtract(const Duration(days: 2)),
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 2)),
      ),
      Debt(
        id: '2',
        customerId: customerId,
        businessOwnerId: 'owner_123',
        amount: 75.0,
        description: 'فاتورة خضار وفواكه',
        isPaid: true,
        dateCreated: now.subtract(const Duration(days: 5)),
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      Debt(
        id: '3',
        customerId: customerId,
        businessOwnerId: 'owner_123',
        amount: 200.0,
        description: 'مواد تنظيف ومنظفات',
        isPaid: false,
        dateCreated: now.subtract(const Duration(days: 7)),
        createdAt: now.subtract(const Duration(days: 7)),
        updatedAt: now.subtract(const Duration(days: 7)),
      ),
      Debt(
        id: '4',
        customerId: customerId,
        businessOwnerId: 'owner_123',
        amount: 120.0,
        description: 'أدوات مكتبية وقرطاسية',
        isPaid: true,
        dateCreated: now.subtract(const Duration(days: 10)),
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 3)),
      ),
      Debt(
        id: '5',
        customerId: customerId,
        businessOwnerId: 'owner_123',
        amount: 300.0,
        description: 'مشتريات شهرية متنوعة',
        isPaid: false,
        dateCreated: now.subtract(const Duration(days: 15)),
        createdAt: now.subtract(const Duration(days: 15)),
        updatedAt: now.subtract(const Duration(days: 15)),
      ),
    ];
  }

  List<Debt> get _filteredDebts {
    List<Debt> filtered = _debts;
    
    // Apply status filter
    switch (_selectedFilter) {
      case 'paid':
        filtered = filtered.where((debt) => debt.isPaid).toList();
        break;
      case 'unpaid':
        filtered = filtered.where((debt) => !debt.isPaid).toList();
        break;
    }
    
    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((debt) => 
        (debt.description ?? '').toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
    }
    
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'الديون',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF0A0E27),
        elevation: 0,
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          Container(
            margin: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
              ),
            ),
            child: IconButton(
              padding: EdgeInsets.zero,
              icon: const Icon(Icons.arrow_back, color: Colors.white, size: 20),
              onPressed: () => Navigator.pop(context),
            ),
          ),
        ],
        leading: Container(
          margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
            ),
          ),
          child: IconButton(
            padding: EdgeInsets.zero,
            icon: const Icon(Icons.refresh, color: Colors.white, size: 20),
            onPressed: _loadDebts,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0A0E27)),
              ),
            )
          : FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  children: [
                    _buildHeader(),
                    _buildFilterSection(),
                    Expanded(child: _buildDebtsList()),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildHeader() {
    final totalDebts = _debts.length;
    final paidDebts = _debts.where((debt) => debt.isPaid).length;
    final unpaidDebts = _debts.where((debt) => !debt.isPaid).length;
    final totalAmount = _debts.fold<double>(0, (sum, debt) => sum + debt.amount);
    final remainingAmount = _debts.fold<double>(0, (sum, debt) => sum + debt.remainingAmount);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص الديون',
            style: TextStyle(
              color: Colors.white,
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  title: 'إجمالي الديون',
                  value: '$totalDebts',
                  icon: Icons.receipt_long,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryItem(
                  title: 'المدفوع',
                  value: '$paidDebts',
                  icon: Icons.check_circle,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryItem(
                  title: 'المتبقي',
                  value: '$unpaidDebts',
                  icon: Icons.schedule,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildAmountItem(
                  title: 'إجمالي المبلغ',
                  amount: totalAmount,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildAmountItem(
                  title: 'المبلغ المتبقي',
                  amount: remainingAmount,
                  color: Colors.yellow.shade300,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAmountItem({
    required String title,
    required double amount,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: color.withOpacity(0.9),
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${amount.toStringAsFixed(2)} ريال',
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: const Color(0xFFE9ECEF)),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: const InputDecoration(
                hintText: 'البحث في الديون...',
                hintStyle: TextStyle(color: Color(0xFF7F8C8D)),
                border: InputBorder.none,
                icon: Icon(Icons.search, color: Color(0xFF7F8C8D)),
              ),
              textDirection: TextDirection.rtl,
            ),
          ),
          const SizedBox(height: 16),
          // Filter Chips
          Row(
            children: [
              _buildFilterChip('all', 'الكل'),
              const SizedBox(width: 8),
              _buildFilterChip('unpaid', 'غير مدفوع'),
              const SizedBox(width: 8),
              _buildFilterChip('paid', 'مدفوع'),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedFilter = value;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF667eea) : const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? const Color(0xFF667eea) : const Color(0xFFE9ECEF),
            ),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected ? Colors.white : const Color(0xFF7F8C8D),
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDebtsList() {
    final filteredDebts = _filteredDebts;

    if (filteredDebts.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredDebts.length,
      itemBuilder: (context, index) {
        final debt = filteredDebts[index];
        return _buildDebtCard(debt, index);
      },
    );
  }

  Widget _buildEmptyState() {
    String message;
    IconData icon;

    switch (_selectedFilter) {
      case 'paid':
        message = 'لا توجد ديون مدفوعة';
        icon = Icons.check_circle_outline;
        break;
      case 'unpaid':
        message = 'لا توجد ديون غير مدفوعة';
        icon = Icons.schedule;
        break;
      default:
        message = 'لا توجد ديون';
        icon = Icons.receipt_long;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF7F8C8D),
              size: 60,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            message,
            style: const TextStyle(
              color: Color(0xFF2C3E50),
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'ستظهر هنا جميع الديون والمعاملات',
            style: TextStyle(
              color: Color(0xFF7F8C8D),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDebtCard(Debt debt, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: debt.isPaid
              ? const Color(0xFF27AE60).withOpacity(0.3)
              : const Color(0xFFE74C3C).withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Status Icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: debt.isPaid
                        ? const Color(0xFF27AE60).withOpacity(0.1)
                        : const Color(0xFFE74C3C).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    debt.isPaid ? Icons.check_circle : Icons.schedule,
                    color: debt.isPaid
                        ? const Color(0xFF27AE60)
                        : const Color(0xFFE74C3C),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                // Debt Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        debt.description ?? 'وصف غير متوفر',
                        style: const TextStyle(
                          color: Color(0xFF2C3E50),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatDate(debt.createdAt),
                        style: const TextStyle(
                          color: Color(0xFF7F8C8D),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                // Status Badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: debt.isPaid
                        ? const Color(0xFF27AE60)
                        : const Color(0xFFE74C3C),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    debt.isPaid ? 'مدفوع' : 'غير مدفوع',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Amount Info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: _buildDebtAmountInfo(
                      'المبلغ الأصلي',
                      debt.amount,
                      const Color(0xFF3498DB),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDebtAmountInfo(
                      'المبلغ المتبقي',
                      debt.remainingAmount,
                      debt.isPaid ? const Color(0xFF27AE60) : const Color(0xFFE74C3C),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebtAmountInfo(String label, double amount, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Color(0xFF7F8C8D),
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(2)} ريال',
          style: TextStyle(
            color: color,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
