import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/device_activation_service.dart';
import '../../utils/responsive_helper.dart';
import 'profile_screen.dart';
import 'customers_screen.dart';
import 'employees_screen.dart';
import 'payment_screen.dart';
import 'reports_screen.dart';

/// Modern Business Owner Dashboard Screen with new design
class ModernBusinessOwnerDashboardScreen extends StatefulWidget {
  const ModernBusinessOwnerDashboardScreen({super.key});

  @override
  State<ModernBusinessOwnerDashboardScreen> createState() => _ModernBusinessOwnerDashboardScreenState();
}

class _ModernBusinessOwnerDashboardScreenState extends State<ModernBusinessOwnerDashboardScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  PageController _pageController = PageController();
  int _currentPage = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    _startAutoSlide();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoSlide() {
    _timer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (_pageController.hasClients) {
        int nextPage = (_currentPage + 1) % 3;
        _pageController.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Simulate loading
      await Future.delayed(const Duration(seconds: 1));
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل البيانات: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0A0E27), // Very dark navy
              Color(0xFF1A1F3A), // Dark navy
              Color(0xFF2D3561), // Medium navy
            ],
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          endDrawer: _buildDrawer(),
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            toolbarHeight: 70,
            title: const Text(
              'الرئيسية',
              style: TextStyle(
                color: Colors.white,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            centerTitle: true,
            leading: Container(
              margin: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications_outlined, color: Colors.white, size: 24),
                    onPressed: () {
                      Navigator.pushNamed(context, '/notifications');
                    },
                  ),
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Builder(
                  builder: (context) => IconButton(
                    icon: const Icon(Icons.menu, color: Colors.white, size: 24),
                    onPressed: () => Scaffold.of(context).openEndDrawer(),
                  ),
                ),
              ),
            ],
          ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator(color: Colors.white))
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadDashboardData,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF3B82F6),
                        ),
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                )
              : _buildDashboardContent(),
          ),
        ),
    );
  }

  Widget _buildDashboardContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Image Slider Section
          _buildImageSlider(),

          SizedBox(height: context.responsivePadding(16)), // مسافة بين الصور والإحصائيات

          // Statistics Section
          _buildStatisticsSection(),

          SizedBox(height: context.responsivePadding(16)), // مسافة بين الإحصائيات والمربعات

          // Main Menu Grid
          _buildMainMenuGrid(),

          SizedBox(height: context.responsivePadding(12)), // مسافة بين المربعات والعمليات

          // Operations Section
          _buildOperationsSection(),
        ],
      ),
    );
  }

  Widget _buildImageSlider() {
    return Column(
      children: [
        Container(
          height: context.sliderHeight,
          margin: EdgeInsets.fromLTRB(context.responsiveHorizontalPadding, 16, context.responsiveHorizontalPadding, 0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(context.responsiveBorderRadius(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 16,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.04),
                blurRadius: 4,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(context.responsiveBorderRadius(20)),
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                  children: [
                    _buildSliderItem(
                      'مرحباً بك في نظام دائن مدين',
                      'نظام شامل لإدارة الديون والمدفوعات',
                      Icons.account_balance_wallet,
                      const Color(0xFF3498DB),
                    ),
                    _buildSliderItem(
                      'إدارة سهلة وفعالة للديون',
                      'تتبع جميع المعاملات المالية بسهولة',
                      Icons.trending_up,
                      const Color(0xFF27AE60),
                    ),
                    _buildSliderItem(
                      'تقارير مفصلة ودقيقة',
                      'احصل على تحليلات شاملة لأعمالك',
                      Icons.analytics,
                      const Color(0xFF9B59B6),
                    ),
                  ],
                ),
              ),
              // Navigation arrows with circular background
              Positioned(
                left: 16,
                top: 0,
                bottom: 0,
                child: Center(
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 18),
                      onPressed: () {
                        if (_currentPage > 0) {
                          _pageController.previousPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        }
                      },
                    ),
                  ),
                ),
              ),
              Positioned(
                right: 16,
                top: 0,
                bottom: 0,
                child: Center(
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_forward_ios, color: Colors.white, size: 18),
                      onPressed: () {
                        if (_currentPage < 2) {
                          _pageController.nextPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        }
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Page indicators outside the image container
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(3, (index) {
              bool isActive = _currentPage == index;
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: isActive ? 24 : 8,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: isActive ? Colors.white : Colors.white.withOpacity(0.4),
                  boxShadow: isActive ? [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildSliderItem(String title, String subtitle, IconData icon, Color color) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.05),
            color.withOpacity(0.1),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color.withOpacity(0.15),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                icon,
                size: 36,
                color: color,
              ),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: context.responsiveFontSize(16),
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 6),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: context.responsiveFontSize(12),
                      color: color.withOpacity(0.8),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainMenuGrid() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.responsiveHorizontalPadding),
      child: GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: context.gridCrossAxisCount,
        crossAxisSpacing: context.gridSpacing,
        mainAxisSpacing: context.gridSpacing,
        childAspectRatio: context.gridChildAspectRatio,
        children: [
          _buildMenuCard(
            icon: Icons.payment,
            title: 'تسديد الديون',
            color: const Color(0xFF4CAF50),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PaymentScreen(),
                ),
              );
            },
          ),
          _buildMenuCard(
            icon: Icons.receipt_long,
            title: 'تسجيل الدين',
            color: const Color(0xFF3498DB),
            onTap: () {
              Navigator.pushNamed(context, '/add_debt');
            },
          ),
          _buildMenuCard(
            icon: Icons.people,
            title: 'إدارة العملاء',
            color: const Color(0xFF9B59B6),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const CustomersScreen()),
              );
            },
          ),
          _buildMenuCard(
            icon: Icons.groups,
            title: 'إدارة الموظفين',
            color: const Color(0xFF34495E),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const EmployeesScreen()),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOperationsSection() {
    return Column(
      children: [
        const SizedBox(height: 16),

        // Additional menu items (النسخ الاحتياطية والتقارير)
        Padding(
          padding: EdgeInsets.symmetric(horizontal: context.responsiveHorizontalPadding),
          child: GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: context.gridCrossAxisCount,
            crossAxisSpacing: context.gridSpacing,
            mainAxisSpacing: context.gridSpacing,
            childAspectRatio: context.gridChildAspectRatio,
            children: [
              _buildMenuCard(
                icon: Icons.cloud_sync,
                title: 'النسخ الاحتياطية',
                color: const Color(0xFF1ABC9C),
                onTap: () {
                  Navigator.pushNamed(context, '/backup');
                },
              ),
              _buildMenuCard(
                icon: Icons.assessment,
                title: 'التقارير',
                color: const Color(0xFF27AE60),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const ReportsScreen()),
                  );
                },
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Operations section with white background
        Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(32),
              topRight: Radius.circular(32),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2C3E50).withOpacity(0.05),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: const Color(0xFF2C3E50).withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: const Text(
                    'العمليات',
                    style: TextStyle(
                      color: Color(0xFF2C3E50),
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 20),

                // Quick Actions
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey.shade200,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.flash_on,
                        size: 32,
                        color: const Color(0xFF3498DB),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'إجراءات سريعة',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildQuickActionButton(
                            'إدارة الديون',
                            Icons.account_balance_wallet,
                            () => Navigator.pushNamed(context, '/debts_management'),
                          ),
                          _buildQuickActionButton(
                            'النسخ الاحتياطي',
                            Icons.backup,
                            () => Navigator.pushNamed(context, '/backup'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuCard({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(context.responsiveBorderRadius(20)),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(context.responsiveBorderRadius(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 12,
                offset: const Offset(0, 6),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.04),
                blurRadius: 4,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(context.responsivePadding(6)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.all(context.responsivePadding(12)),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.12),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: color.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    size: context.responsiveIconSize(28),
                    color: color,
                  ),
                ),
                SizedBox(height: context.responsivePadding(8)),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: context.responsiveFontSize(13),
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2C3E50),
                    letterSpacing: 0.1,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      backgroundColor: Colors.white,
      child: Column(
        children: [
          // Header Section
          GestureDetector(
            onTap: () {
              Navigator.pop(context); // إغلاق القائمة الجانبية
              Navigator.pushNamed(context, '/profile');
            },
            child: Container(
              height: 200,
              width: double.infinity,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF0A0E27),
                    Color(0xFF1A1F3A),
                    Color(0xFF2D3561),
                  ],
                ),
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          final user = authProvider.userProfile;
                          return Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(40),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: _buildProfileImage(user),
                          );
                        },
                      ),
                      const SizedBox(height: 12),
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          final businessOwner = authProvider.userProfile;
                          return Column(
                            children: [
                              Text(
                                businessOwner?.businessName ?? 'محل عثمان الحمادي',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                                textDirection: TextDirection.ltr,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'حالة التطبيق: مفعل',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                                textDirection: TextDirection.ltr,
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Menu Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.file_download,
                  title: 'استيراد العملاء والموظفين',
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم بدء عملية استيراد البيانات'),
                        backgroundColor: Color(0xFF27AE60),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.verified,
                  title: 'تفعيل التطبيق',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/subscription_plans');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.share,
                  title: 'مشاركة رمز التفعيل',
                  onTap: () {
                    Navigator.pop(context);
                    _showActivationCodeDialog();
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.contact_support,
                  title: 'تواصل معنا',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/contact_us_business');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.play_circle_outline,
                  title: 'شرح التطبيق',
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('شرح التطبيق قريباً')),
                    );
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.privacy_tip,
                  title: 'سياسة الخصوصية',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/privacy_policy');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.settings,
                  title: 'الإعدادات',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/settings');
                  },
                ),
                const Divider(height: 1),
                _buildDrawerItem(
                  icon: Icons.logout,
                  title: 'تسجيل الخروج',
                  textColor: Colors.red,
                  iconColor: Colors.red,
                  onTap: () {
                    Navigator.pop(context);
                    _showLogoutConfirmationDialog();
                  },
                ),
              ],
            ),
          ),

          // Footer
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                top: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'مواقع التواصل الاجتماعي',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildSocialIcon(Icons.facebook, Colors.blue.shade700),
                    _buildSocialIcon(Icons.alternate_email, Colors.black),
                    _buildSocialIcon(Icons.phone, Colors.green.shade600),
                    _buildSocialIcon(Icons.language, Colors.orange.shade600),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  'برمجة وتطوير شركة\nحقوق محفوظة لدى شركة',
                  style: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
    Color? iconColor,
  }) {
    return ListTile(
      trailing: Icon(
        icon,
        color: iconColor ?? Colors.grey.shade700,
        size: 24,
      ),
      title: Align(
        alignment: Alignment.centerRight,
        child: Text(
          title,
          style: TextStyle(
            color: textColor ?? Colors.grey.shade800,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.right,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      hoverColor: Colors.grey.shade100,
    );
  }

  Widget _buildSocialIcon(IconData icon, Color color) {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Icon(
        icon,
        color: color,
        size: 18,
      ),
    );
  }

  void _showActivationCodeDialog() async {
    try {
      // Get real activation data from the same service used in contact_us_screen
      final activationService = DeviceActivationService.instance;
      final activationCode = await activationService.getDeviceActivationCode();
      final deviceId = await activationService.getDeviceId();

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(
                  Icons.share,
                  color: Colors.blue.shade600,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'رمز التفعيل',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDialogInfoRow('رمز التفعيل', activationCode),
                const SizedBox(height: 16),
                _buildDialogInfoRow('معرف الجهاز', deviceId.length > 20 ? '${deviceId.substring(0, 20)}...' : deviceId),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'إغلاق',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          );
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل معلومات التفعيل: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildDialogInfoRow(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE5E7EB),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF1F2937),
                    fontWeight: FontWeight.bold,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: value));
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم نسخ $label'),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            icon: Icon(
              Icons.copy,
              color: Colors.blue.shade600,
              size: 20,
            ),
            tooltip: 'نسخ $label',
            style: IconButton.styleFrom(
              backgroundColor: Colors.blue.shade50,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(String title, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: const Color(0xFF3498DB).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFF3498DB).withOpacity(0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: const Color(0xFF3498DB),
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                color: Color(0xFF3498DB),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutConfirmationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.logout,
                color: Colors.red.shade600,
                size: 28,
              ),
              const SizedBox(width: 12),
              const Text(
                'تسجيل الخروج',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: const Text(
            'هل أنت متأكد من أنك تريد تسجيل الخروج من التطبيق؟',
            style: TextStyle(
              fontSize: 16,
              height: 1.4,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'لا',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                // حفظ مرجع للـ Navigator قبل أي عملية
                final navigator = Navigator.of(context);

                navigator.pop(); // إغلاق النافذة

                // تسجيل الخروج
                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                await authProvider.signOut();

                // انتظار قصير ثم التنقل
                await Future.delayed(const Duration(milliseconds: 100));

                // التنقل المباشر لصفحة تسجيل الدخول
                navigator.pushNamedAndRemoveUntil(
                  '/login',
                  (route) => false,
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'نعم',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatisticsSection() {
    return Container(
      margin: EdgeInsets.fromLTRB(context.responsiveHorizontalPadding, 0, context.responsiveHorizontalPadding, 0),
      padding: EdgeInsets.all(context.responsivePadding(20)),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFE9ECEF), // رمادي فاتح
            Color(0xFFDEE2E6), // رمادي أغمق قليلاً
          ],
        ),
        borderRadius: BorderRadius.circular(context.responsiveBorderRadius(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C757D).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.trending_up,
                  color: Color(0xFF6C757D),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'الملخص المالي',
                style: TextStyle(
                  fontSize: context.responsiveFontSize(17),
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF495057),
                ),
              ),
            ],
          ),
          SizedBox(height: context.responsivePadding(16)),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الديون',
                  '15,750 ر.س',
                  Icons.account_balance_wallet,
                  const Color(0xFFE74C3C),
                ),
              ),
              SizedBox(width: context.gridSpacing),
              Expanded(
                child: _buildStatCard(
                  'إجمالي السداد',
                  '8,250 ر.س',
                  Icons.payments,
                  const Color(0xFF27AE60),
                ),
              ),
            ],
          ),
          SizedBox(height: context.gridSpacing),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'عدد العملاء',
                  '24',
                  Icons.people,
                  const Color(0xFF3498DB),
                ),
              ),
              SizedBox(width: context.gridSpacing),
              Expanded(
                child: _buildStatCard(
                  'عدد الموظفين',
                  '3',
                  Icons.groups,
                  const Color(0xFF9B59B6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(context.responsivePadding(16)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(context.responsiveBorderRadius(12)),
        border: Border.all(
          color: const Color(0xFFE9ECEF),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: context.responsiveIconSize(18),
              ),
              SizedBox(width: context.responsivePadding(6)),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: context.responsiveFontSize(12),
                    color: const Color(0xFF6C757D),
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: context.responsivePadding(8)),
          Text(
            value,
            style: TextStyle(
              fontSize: context.responsiveFontSize(15),
              fontWeight: FontWeight.bold,
              color: const Color(0xFF212529),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileImage(dynamic user) {
    if (user?.profileImagePath != null && user!.profileImagePath!.isNotEmpty) {
      // عرض الصورة المحفوظة
      if (user.profileImagePath!.startsWith('data:image')) {
        // صورة base64
        try {
          final base64String = user.profileImagePath!.split(',')[1];
          final bytes = base64Decode(base64String);
          return ClipRRect(
            borderRadius: BorderRadius.circular(37),
            child: Image.memory(
              bytes,
              width: 74,
              height: 74,
              fit: BoxFit.cover,
            ),
          );
        } catch (e) {
          // في حالة خطأ في فك تشفير الصورة، عرض الأيقونة الافتراضية
          return const Icon(
            Icons.store,
            size: 40,
            color: Color(0xFF0A0E27),
          );
        }
      }
    }

    // عرض الأيقونة الافتراضية
    return const Icon(
      Icons.store,
      size: 40,
      color: Color(0xFF0A0E27),
    );
  }
}
