import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/models.dart';
import '../../providers/auth_provider.dart';
import '../../services/debt_service.dart';

class RemoteDebtScreen extends StatefulWidget {
  const RemoteDebtScreen({super.key});

  @override
  State<RemoteDebtScreen> createState() => _RemoteDebtScreenState();
}

class _RemoteDebtScreenState extends State<RemoteDebtScreen> with TickerProviderStateMixin {
  String _operationType = 'payment'; // payment, debt
  String _paymentMethod = 'cash'; // cash, transfer, wallet
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _transferNumberController = TextEditingController();
  final _transactionNumberController = TextEditingController();
  bool _isSubmitting = false;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    _transferNumberController.dispose();
    _transactionNumberController.dispose();
    super.dispose();
  }

  Future<void> _submitRequest() async {
    if (!_validateForm()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final customer = authProvider.userProfile;

      if (customer != null) {
        // Create remote debt request
        final request = RemoteDebtRequest.create(
          customerId: customer.id,
          businessOwnerId: customer.businessOwnerId,
          operationType: _operationType,
          amount: double.parse(_amountController.text),
          paymentMethod: _operationType == 'payment' ? _paymentMethod : null,
          description: _descriptionController.text.trim(),
          transferNumber: _operationType == 'payment' ? _transferNumberController.text.trim() : null,
          transactionNumber: _operationType == 'payment' ? _transactionNumberController.text.trim() : null,
        );

        // Submit request (simulate for now)
        await Future.delayed(const Duration(seconds: 2));
        
        setState(() {
          _isSubmitting = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _operationType == 'payment' 
                    ? 'تم إرسال طلب السداد بنجاح، في انتظار موافقة مالك المنشأة'
                    : 'تم إرسال طلب تسجيل الدين بنجاح، في انتظار موافقة مالك المنشأة'
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
          
          // Clear form
          _clearForm();
        }
      }
    } catch (e) {
      setState(() {
        _isSubmitting = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الطلب: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  bool _validateForm() {
    if (_amountController.text.isEmpty) {
      _showError('يرجى إدخال المبلغ');
      return false;
    }

    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      _showError('يرجى إدخال مبلغ صحيح');
      return false;
    }

    if (_descriptionController.text.trim().isEmpty) {
      _showError('يرجى إدخال وصف العملية');
      return false;
    }

    // التحقق من طرق الدفع فقط عند السداد
    if (_operationType == 'payment') {
      if (_paymentMethod == 'transfer' && _transferNumberController.text.trim().isEmpty) {
        _showError('يرجى إدخال رقم الحوالة');
        return false;
      }

      if (_paymentMethod == 'wallet' && _transactionNumberController.text.trim().isEmpty) {
        _showError('يرجى إدخال رقم العملية');
        return false;
      }
    }

    return true;
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _clearForm() {
    _amountController.clear();
    _descriptionController.clear();
    _transferNumberController.clear();
    _transactionNumberController.clear();
    setState(() {
      _operationType = 'payment';
      _paymentMethod = 'cash';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'تسجيل دين من بعد',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF0A0E27),
        elevation: 0,
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          Container(
            margin: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
              ),
            ),
            child: IconButton(
              padding: EdgeInsets.zero,
              icon: const Icon(Icons.arrow_back, color: Colors.white, size: 20),
              onPressed: () => Navigator.pop(context),
            ),
          ),
        ],
        leading: Container(
          margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
            ),
          ),
          child: IconButton(
            padding: EdgeInsets.zero,
            icon: const Icon(Icons.refresh, color: Colors.white, size: 20),
            onPressed: _clearForm,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 24),
                _buildOperationTypeSelection(),
                const SizedBox(height: 24),
                _buildAmountSection(),
                const SizedBox(height: 24),
                // إظهار طرق الدفع فقط عند السداد
                if (_operationType == 'payment') _buildPaymentMethodSelection(),
                if (_operationType == 'payment') const SizedBox(height: 24),
                if (_operationType == 'payment' && _paymentMethod != 'cash') _buildPaymentDetails(),
                if (_operationType == 'payment' && _paymentMethod != 'cash') const SizedBox(height: 24),
                _buildDescriptionSection(),
                const SizedBox(height: 32),
                _buildSubmitButton(),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'طلب عملية من بعد',
            style: TextStyle(
              color: Colors.white,
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'يمكنك تسجيل دين جديد أو سداد دين موجود من بعد',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Row(
              children: [
                Icon(Icons.info_outline, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'سيتم إرسال طلبك لمالك المنشأة للموافقة عليه',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOperationTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نوع العملية',
          style: TextStyle(
            color: Color(0xFF2C3E50),
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildOperationTypeCard(
                type: 'payment',
                title: 'سداد دين',
                subtitle: 'سداد دين موجود',
                icon: Icons.payment,
                color: const Color(0xFF27AE60),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildOperationTypeCard(
                type: 'debt',
                title: 'تسجيل دين',
                subtitle: 'إضافة دين جديد',
                icon: Icons.add_circle_outline,
                color: const Color(0xFFE74C3C),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOperationTypeCard({
    required String type,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    final isSelected = _operationType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _operationType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? color : const Color(0xFFE9ECEF),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : const Color(0xFF7F8C8D),
              size: 32,
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? color : const Color(0xFF2C3E50),
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: isSelected ? color.withOpacity(0.8) : const Color(0xFF7F8C8D),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _operationType == 'payment' ? Icons.money_off : Icons.attach_money,
                color: _operationType == 'payment' ? const Color(0xFF27AE60) : const Color(0xFFE74C3C),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                _operationType == 'payment' ? 'مبلغ السداد' : 'مبلغ الدين',
                style: const TextStyle(
                  color: Color(0xFF2C3E50),
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _amountController,
            keyboardType: TextInputType.number,
            textDirection: TextDirection.ltr,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            decoration: InputDecoration(
              hintText: 'أدخل المبلغ',
              hintStyle: const TextStyle(color: Color(0xFF7F8C8D)),
              prefixIcon: const Icon(Icons.attach_money, color: Color(0xFF4facfe)),
              suffixText: 'ريال',
              suffixStyle: const TextStyle(
                color: Color(0xFF2C3E50),
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFE9ECEF)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF4facfe), width: 2),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.all(16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'طريقة الدفع',
          style: TextStyle(
            color: Color(0xFF2C3E50),
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildPaymentMethodCard(
                    method: 'cash',
                    title: 'نقداً',
                    subtitle: 'دفع نقدي',
                    icon: Icons.money,
                    color: const Color(0xFF27AE60),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPaymentMethodCard(
                    method: 'transfer',
                    title: 'حوالة',
                    subtitle: 'حوالة بنكية',
                    icon: Icons.account_balance,
                    color: const Color(0xFF3498DB),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildPaymentMethodCard(
              method: 'wallet',
              title: 'محفظة إلكترونية',
              subtitle: 'دفع عبر المحفظة الإلكترونية',
              icon: Icons.account_balance_wallet,
              color: const Color(0xFF9B59B6),
              isFullWidth: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaymentMethodCard({
    required String method,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    bool isFullWidth = false,
  }) {
    final isSelected = _paymentMethod == method;

    return GestureDetector(
      onTap: () {
        setState(() {
          _paymentMethod = method;
        });
      },
      child: Container(
        width: isFullWidth ? double.infinity : null,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : const Color(0xFFE9ECEF),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: isFullWidth
            ? Row(
                children: [
                  Icon(
                    icon,
                    color: isSelected ? color : const Color(0xFF7F8C8D),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            color: isSelected ? color : const Color(0xFF2C3E50),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          subtitle,
                          style: TextStyle(
                            color: isSelected ? color.withOpacity(0.8) : const Color(0xFF7F8C8D),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )
            : Column(
                children: [
                  Icon(
                    icon,
                    color: isSelected ? color : const Color(0xFF7F8C8D),
                    size: 24,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    title,
                    style: TextStyle(
                      color: isSelected ? color : const Color(0xFF2C3E50),
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: isSelected ? color.withOpacity(0.8) : const Color(0xFF7F8C8D),
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildPaymentDetails() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _paymentMethod == 'transfer' ? Icons.receipt_long : Icons.qr_code,
                color: const Color(0xFF4facfe),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                _paymentMethod == 'transfer' ? 'تفاصيل الحوالة' : 'تفاصيل المحفظة',
                style: const TextStyle(
                  color: Color(0xFF2C3E50),
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _paymentMethod == 'transfer' ? _transferNumberController : _transactionNumberController,
            keyboardType: TextInputType.text,
            textDirection: TextDirection.ltr,
            decoration: InputDecoration(
              hintText: _paymentMethod == 'transfer' ? 'رقم الحوالة' : 'رقم العملية',
              hintStyle: const TextStyle(color: Color(0xFF7F8C8D)),
              prefixIcon: Icon(
                _paymentMethod == 'transfer' ? Icons.receipt : Icons.confirmation_number,
                color: const Color(0xFF4facfe),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFE9ECEF)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF4facfe), width: 2),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'وصف العملية',
          style: TextStyle(
            color: Color(0xFF2C3E50),
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _descriptionController,
          maxLines: 4,
          textDirection: TextDirection.rtl,
          decoration: InputDecoration(
            hintText: 'أدخل وصف تفصيلي للعملية...',
            hintStyle: const TextStyle(color: Color(0xFF7F8C8D)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE9ECEF)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF4facfe), width: 2),
            ),
            filled: true,
            fillColor: const Color(0xFFF8F9FA),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitRequest,
        style: ElevatedButton.styleFrom(
          backgroundColor: _operationType == 'payment'
              ? const Color(0xFF27AE60)
              : const Color(0xFFE74C3C),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          disabledBackgroundColor: const Color(0xFF95A5A6),
        ),
        child: _isSubmitting
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text(
                    'جاري الإرسال...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _operationType == 'payment' ? Icons.send : Icons.add_circle,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _operationType == 'payment' ? 'إرسال طلب السداد' : 'إرسال طلب التسجيل',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

// Model for remote debt request
class RemoteDebtRequest {
  final String id;
  final String customerId;
  final String businessOwnerId;
  final String operationType;
  final double amount;
  final String? paymentMethod;
  final String description;
  final String? transferNumber;
  final String? transactionNumber;
  final DateTime createdAt;
  final String status; // pending, approved, rejected

  RemoteDebtRequest({
    required this.id,
    required this.customerId,
    required this.businessOwnerId,
    required this.operationType,
    required this.amount,
    this.paymentMethod,
    required this.description,
    this.transferNumber,
    this.transactionNumber,
    required this.createdAt,
    required this.status,
  });

  factory RemoteDebtRequest.create({
    required String customerId,
    required String businessOwnerId,
    required String operationType,
    required double amount,
    String? paymentMethod,
    required String description,
    String? transferNumber,
    String? transactionNumber,
  }) {
    return RemoteDebtRequest(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      customerId: customerId,
      businessOwnerId: businessOwnerId,
      operationType: operationType,
      amount: amount,
      paymentMethod: paymentMethod,
      description: description,
      transferNumber: transferNumber,
      transactionNumber: transactionNumber,
      createdAt: DateTime.now(),
      status: 'pending',
    );
  }
}
